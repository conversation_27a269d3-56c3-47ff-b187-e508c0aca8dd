{"log": {"entries": [{"request": {"method": "POST", "bodySize": 89, "headersSize": 1657, "postData": {"params": [], "text": "{\"id\":\"ec9dc47120a64a53be67f3a91d8b7c11\",\"taskId\":\"taskdf5ea7d0ff824406b40a980bccbc88cf\"}", "mimeType": "application/json"}, "cookies": [{"name": "QZ_SID", "value": "********************************"}, {"name": "sensorsdata2015jssdkcross", "value": "%7B%22%24device_id%22%3A%22198612bffc9131-0635a87d4bc8184-5611415e-250125-198612bffca2b9%22%7D"}, {"name": "sa_jssdk_2015_m_topsports_com_cn", "value": "%7B%22distinct_id%22%3A%228afbd03a87bc6ad701881d707f175358%22%2C%22first_id%22%3A%228afbd03a87bc6ad701881d707f175358%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk4NjEyYmZmYzkxMzEtMDYzNWE4N2Q0YmM4MTg0LTU2MTE0MTVlLTI1MDEyNS0xOTg2MTJiZmZjYTJiOSIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjhhZmJkMDNhODdiYzZhZDcwMTg4MWQ3MDdmMTc1MzU4IiwiJGlkZW50aXR5X2Fub255bW91c19pZCI6IjhhZmJkMDNhODdiYzZhZDcwMTg4MWQ3MDdmMTc1MzU4In0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%228afbd03a87bc6ad701881d707f175358%22%7D%7D"}, {"name": "sajssdk_2015_new_user_m_topsports_com_cn", "value": "1"}, {"name": "Authorization", "value": "e0f4ae65-7e9f-4043-808c-fb949f1401f5"}, {"name": "appletsSource", "value": "wx71a6af1f91734f18"}, {"name": "memberId", "value": "8afbd03a87bc6ad701881d707f175358"}, {"name": "version", "value": "4.5.2"}, {"name": "acw_tc", "value": "3ccdc14b17539769277015264e45625c8d2dadd1665f1508e7adcacbbcb98a"}], "headers": [{"name": "Host", "value": "m.topsports.com.cn"}, {"name": "brandCode", "value": "TS"}, {"name": "Accept", "value": "application/json, text/plain, */*"}, {"name": "version", "value": "4.5.2"}, {"name": "Accept-Language", "value": "zh-cn"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Origin", "value": "https://m.topsports.com.cn"}, {"name": "User-Agent", "value": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_8_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.59(0x18003b2e) NetType/WIFI Language/zh_CN miniProgram/wx71a6af1f91734f18"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://m.topsports.com.cn/m/dailycenter?brandCode=TS&share=true&minienv=1"}, {"name": "Content-Length", "value": "89"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON>", "value": "QZ_SID=********************************; sensorsdata2015jssdkcross=%7B%22%24device_id%22%3A%22198612bffc9131-0635a87d4bc8184-5611415e-250125-198612bffca2b9%22%7D; sa_jssdk_2015_m_topsports_com_cn=%7B%22distinct_id%22%3A%228afbd03a87bc6ad701881d707f175358%22%2C%22first_id%22%3A%228afbd03a87bc6ad701881d707f175358%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk4NjEyYmZmYzkxMzEtMDYzNWE4N2Q0YmM4MTg0LTU2MTE0MTVlLTI1MDEyNS0xOTg2MTJiZmZjYTJiOSIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjhhZmJkMDNhODdiYzZhZDcwMTg4MWQ3MDdmMTc1MzU4IiwiJGlkZW50aXR5X2Fub255bW91c19pZCI6IjhhZmJkMDNhODdiYzZhZDcwMTg4MWQ3MDdmMTc1MzU4In0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%228afbd03a87bc6ad701881d707f175358%22%7D%7D; sajssdk_2015_new_user_m_topsports_com_cn=1; Authorization=e0f4ae65-7e9f-4043-808c-fb949f1401f5; appletsSource=wx71a6af1f91734f18; memberId=8afbd03a87bc6ad701881d707f175358; version=4.5.2; acw_tc=3ccdc14b17539769277015264e45625c8d2dadd1665f1508e7adcacbbcb98a"}], "queryString": [], "httpVersion": "HTTP/1.1", "url": "https://m.topsports.com.cn/h5/taskcenter/receiveReward"}, "timings": {"connect": 446.0000991821289, "send": 330.99985122680664, "dns": -1, "ssl": 315.99998474121094, "wait": 180.9999942779541, "blocked": -1, "receive": 7.000207901000977}, "serverIPAddress": "*************", "response": {"status": 200, "statusText": "OK", "cookies": [], "headers": [{"name": "Date", "value": "Thu, 31 Jul 2025 15:50:47 GMT"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Server", "value": "Tengin<PERSON>"}, {"name": "X-TRACE-ID", "value": "123-843-1753977047076-1503437"}, {"name": "X-Content-Type-Options", "value": "nosniff"}, {"name": "X-XSS-Protection", "value": "1; mode=block"}, {"name": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"name": "Pragma", "value": "no-cache"}, {"name": "Expires", "value": "0"}, {"name": "Strict-Transport-Security", "value": "max-age=31536000 ; includeSubDomains"}, {"name": "X-Frame-Options", "value": "DENY"}, {"name": "Content-Encoding", "value": "gzip"}], "httpVersion": "HTTP/1.1", "content": {"text": "{\"code\":1,\"bizCode\":20000,\"bizMsg\":\"成功\",\"data\":{\"message\":\"领取成功\",\"success\":true,\"prizeList\":[{\"brandCode\":\"TS\",\"memberId\":\"8afbd03a87bc6ad701881d707f175358\",\"transactionId\":\"e5a8d4256dfa426aa605726dbf451e41\",\"businessId\":\"e5a8d4256dfa426aa605726dbf451e41\",\"taskId\":\"taskdf5ea7d0ff824406b40a980bccbc88cf\",\"prizeName\":\"积分\",\"prizeImg\":\"默认图片\",\"prizeType\":3,\"prizeRuleCode\":null,\"prizeValue\":\"2\",\"prizeAmount\":null,\"sendTime\":null,\"prizePoolId\":null}]}}", "size": 472, "mimeType": "application/json"}}, "time": 193.00007820129395, "startedDateTime": "2025-07-31T23:50:47.036Z"}], "pages": [], "creator": {"name": "Stream", "version": "1.0.6"}, "version": "1.2"}}