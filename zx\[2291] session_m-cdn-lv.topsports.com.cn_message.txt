GET /m/assets/index.6ce7abf2.js h2
host: m-cdn-lv.topsports.com.cn
origin: https://m.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: */*
sec-fetch-site: same-site
sec-fetch-mode: cors
sec-fetch-dest: script
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1



h2 200
server: Tengine
content-type: application/javascript; charset=utf-8
content-length: 1051
date: Thu, 31 Jul 2025 12:13:19 GMT
x-trace-id: 123-7064-1753963999787-6445817
last-modified: Thu, 31 Jul 2025 10:28:26 GMT
etag: "688b454a-41b"
accept-ranges: bytes
via: cache2.l2cn8116[0,0,200-0,H], cache50.l2cn8116[1,0], cache50.l2cn8116[1,0], cache3.cn7330[0,0,200-0,H], cache10.cn7330[3,0]
age: 220
ali-swift-global-savetime: 1753963999
x-cache: HIT TCP_MEM_HIT dirn:-2:-2
x-swift-savetime: Thu, 31 Jul 2025 12:16:27 GMT
x-swift-cachetime: 648
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f1e17539642195551606e

import{E as f,d as h,ba as b,aq as k,i as l,N as w,am as t,ai as S,W as y}from"./index.963830f9.js";const[x,c]=f("switch"),z={size:w,loading:Boolean,disabled:Boolean,modelValue:t,activeColor:String,inactiveColor:String,activeValue:{type:t,default:!0},inactiveValue:{type:t,default:!1}};var N=h({name:x,props:z,emits:["change","update:modelValue"],setup(e,{emit:d,slots:n}){const i=()=>e.modelValue===e.activeValue,u=()=>{if(!e.disabled&&!e.loading){const a=i()?e.inactiveValue:e.activeValue;d("update:modelValue",a),d("change",a)}},r=()=>{if(e.loading){const a=i()?e.activeColor:e.inactiveColor;return l(S,{class:c("loading"),color:a},null)}if(n.node)return n.node()};return b(()=>e.modelValue),()=>{var a;const{size:v,loading:m,disabled:s,activeColor:g,inactiveColor:V}=e,o=i(),C={fontSize:k(v),backgroundColor:o?g:V};return l("div",{role:"switch",class:c({on:o,loading:m,disabled:s}),style:C,tabindex:s?void 0:0,"aria-checked":o,onClick:u},[l("div",{class:c("node")},[r()]),(a=n.background)==null?void 0:a.call(n)])}}});const B=y(N);export{B as S};
