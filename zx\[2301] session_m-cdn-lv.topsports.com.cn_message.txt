GET /m/assets/index.ca74db51.js h2
host: m-cdn-lv.topsports.com.cn
origin: https://m.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: */*
sec-fetch-site: same-site
sec-fetch-mode: cors
sec-fetch-dest: script
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1



h2 200
server: Tengine
content-type: application/javascript; charset=utf-8
content-length: 5004
date: Thu, 31 Jul 2025 11:56:12 GMT
x-trace-id: 123-70109-1753962972300-6314351
last-modified: Thu, 31 Jul 2025 08:25:34 GMT
etag: "688b287e-138c"
accept-ranges: bytes
via: cache40.l2cn8116[0,0,200-0,H], cache32.l2cn8116[1,0], cache32.l2cn8116[2,0], cache4.cn7330[0,0,200-0,H], cache10.cn7330[1,0]
age: 1247
ali-swift-global-savetime: 1753962972
x-cache: HIT TCP_MEM_HIT dirn:-2:-2
x-swift-savetime: Thu, 31 Jul 2025 11:56:50 GMT
x-swift-cachetime: 1267
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f1e17539642199032651e

import{E as K,d as Q,g as L,r as U,aP as ue,ad as re,H as f,ak as Z,L as T,aZ as ve,a_ as fe,a$ as de,f as q,aR as he,ac as ge,B as me,C as we,M as be,i as P,ae as I,N as V,P as O,b0 as M,Q as j,ab as F,ah as pe,b1 as D,W as G,aj as ye}from"./index.963830f9.js";const[J,k]=K("swipe"),xe={loop:I,width:V,height:V,vertical:Boolean,autoplay:O(0),duration:O(500),touchable:I,lazyRender:Boolean,initialSwipe:O(0),indicatorColor:String,showIndicators:I,stopPropagation:I},ee=Symbol(J);var Se=Q({name:J,props:xe,emits:["change"],setup(a,{emit:C,slots:h}){const u=L(),d=L(),e=U({rect:null,width:0,height:0,offset:0,active:0,swiping:!1}),r=ue(),{children:g,linkChildren:z}=re(ee),i=f(()=>g.length),l=f(()=>e[a.vertical?"height":"width"]),s=f(()=>a.vertical?r.deltaY.value:r.deltaX.value),m=f(()=>e.rect?(a.vertical?e.rect.height:e.rect.width)-l.value*i.value:0),E=f(()=>Math.ceil(Math.abs(m.value)/l.value)),p=f(()=>i.value*l.value),$=f(()=>(e.active+i.value)%i.value),R=f(()=>{const t=a.vertical?"vertical":"horizontal";return r.direction.value===t}),te=f(()=>{const t={transitionDuration:`${e.swiping?0:a.duration}ms`,transform:`translate${a.vertical?"Y":"X"}(${e.offset}px)`};if(l.value){const o=a.vertical?"height":"width",n=a.vertical?"width":"height";t[o]=`${p.value}px`,t[n]=a[n]?`${a[n]}px`:""}return t}),ae=t=>{const{active:o}=e;return t?a.loop?D(o+t,-1,i.value):D(o+t,0,E.value):o},B=(t,o=0)=>{let n=t*l.value;a.loop||(n=Math.min(n,-m.value));let v=o-n;return a.loop||(v=D(v,m.value,0)),v},w=({pace:t=0,offset:o=0,emitChange:n})=>{if(i.value<=1)return;const{active:v}=e,c=ae(t),S=B(c,o);if(a.loop){if(g[0]&&S!==m.value){const _=S<m.value;g[0].setOffset(_?p.value:0)}if(g[i.value-1]&&S!==0){const _=S>0;g[i.value-1].setOffset(_?-p.value:0)}}e.active=c,e.offset=S,n&&c!==v&&C("change",$.value)},A=()=>{e.swiping=!0,e.active<=-1?w({pace:i.value}):e.active>=i.value&&w({pace:-i.value})},ie=()=>{A(),r.reset(),M(()=>{e.swiping=!1,w({pace:-1,emitChange:!0})})},Y=()=>{A(),r.reset(),M(()=>{e.swiping=!1,w({pace:1,emitChange:!0})})};let H;const y=()=>clearTimeout(H),x=()=>{y(),a.autoplay>0&&i.value>1&&(H=setTimeout(()=>{Y(),x()},+a.autoplay))},b=(t=+a.initialSwipe)=>{if(!u.value)return;const o=()=>{var n,v;if(!j(u)){const c={width:u.value.offsetWidth,height:u.value.offsetHeight};e.rect=c,e.width=+((n=a.width)!=null?n:c.width),e.height=+((v=a.height)!=null?v:c.height)}i.value&&(t=Math.min(i.value-1,t)),e.active=t,e.swiping=!0,e.offset=B(t),g.forEach(c=>{c.setOffset(0)}),x()};j(u)?F().then(o):o()},N=()=>b(e.active);let W;const ne=t=>{!a.touchable||(r.start(t),W=Date.now(),y(),A())},oe=t=>{a.touchable&&e.swiping&&(r.move(t),R.value&&(!a.loop&&(e.active===0&&s.value>0||e.active===i.value-1&&s.value<0)||(pe(t,a.stopPropagation),w({offset:s.value}))))},X=()=>{if(!a.touchable||!e.swiping)return;const t=Date.now()-W,o=s.value/t;if((Math.abs(o)>.25||Math.abs(s.value)>l.value/2)&&R.value){const v=a.vertical?r.offsetY.value:r.offsetX.value;let c=0;a.loop?c=v>0?s.value>0?-1:1:0:c=-Math[s.value>0?"ceil":"floor"](s.value/l.value),w({pace:c,emitChange:!0})}else s.value&&w({pace:0});e.swiping=!1,x()},se=(t,o={})=>{A(),r.reset(),M(()=>{let n;a.loop&&t===i.value?n=e.active===0?0:t:n=t%i.value,o.immediate?M(()=>{e.swiping=!1}):e.swiping=!1,w({pace:n-e.active,emitChange:!0})})},le=(t,o)=>{const n=o===$.value,v=n?{backgroundColor:a.indicatorColor}:void 0;return P("i",{style:v,class:k("indicator",{active:n})},null)},ce=()=>{if(h.indicator)return h.indicator({active:$.value,total:i.value});if(a.showIndicators&&i.value>1)return P("div",{class:k("indicators",{vertical:a.vertical})},[Array(i.value).fill("").map(le)])};return Z({prev:ie,next:Y,state:e,resize:N,swipeTo:se}),z({size:l,props:a,count:i,activeIndicator:$}),T(()=>a.initialSwipe,t=>b(+t)),T(i,()=>b(e.active)),T(()=>a.autoplay,x),T([ve,fe],N),T(de(),t=>{t==="visible"?x():y()}),q(b),he(()=>b(e.active)),ge(()=>b(e.active)),me(y),we(y),be("touchmove",oe,{target:d}),()=>{var t;return P("div",{ref:u,class:k()},[P("div",{ref:d,style:te.value,class:k("track",{vertical:a.vertical}),onTouchstartPassive:ne,onTouchend:X,onTouchcancel:X},[(t=h.default)==null?void 0:t.call(h)]),ce()])}}});const Ae=G(Se),[Te,Pe]=K("swipe-item");var Ce=Q({name:Te,setup(a,{slots:C}){let h;const u=U({offset:0,inited:!1,mounted:!1}),{parent:d,index:e}=ye(ee);if(!d){console.error("[Vant] <SwipeItem> must be a child component of <Swipe>.");return}const r=f(()=>{const i={},{vertical:l}=d.props;return d.size.value&&(i[l?"height":"width"]=`${d.size.value}px`),u.offset&&(i.transform=`translate${l?"Y":"X"}(${u.offset}px)`),i}),g=f(()=>{const{loop:i,lazyRender:l}=d.props;if(!l||h)return!0;if(!u.mounted)return!1;const s=d.activeIndicator.value,m=d.count.value-1,E=s===0&&i?m:s-1,p=s===m&&i?0:s+1;return h=e.value===s||e.value===E||e.value===p,h}),z=i=>{u.offset=i};return q(()=>{F(()=>{u.mounted=!0})}),Z({setOffset:z}),()=>{var i;return P("div",{class:Pe(),style:r.value},[g.value?(i=C.default)==null?void 0:i.call(C):null])}}});const Ie=G(Ce);export{Ae as S,Ie as a};
