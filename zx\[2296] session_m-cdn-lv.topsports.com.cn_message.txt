GET /m/assets/api.5870f003.js h2
host: m-cdn-lv.topsports.com.cn
origin: https://m.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: */*
sec-fetch-site: same-site
sec-fetch-mode: cors
sec-fetch-dest: script
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1



h2 200
server: Tengine
content-type: application/javascript; charset=utf-8
content-length: 6682
date: Thu, 31 Jul 2025 12:15:16 GMT
x-trace-id: 123-7087-1753964116862-6569403
last-modified: Thu, 31 Jul 2025 08:25:34 GMT
etag: "688b287e-1a1a"
accept-ranges: bytes
via: cache24.l2cn8116[0,0,200-0,H], cache40.l2cn8116[1,0], cache40.l2cn8116[1,0], cache2.cn7330[0,0,200-0,H], cache10.cn7330[1,0]
age: 103
ali-swift-global-savetime: 1753964116
x-cache: HIT TCP_MEM_HIT dirn:-2:-2
x-swift-savetime: Thu, 31 Jul 2025 12:16:27 GMT
x-swift-cachetime: 1385
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f1e17539642196251818e

import{R as o}from"./index.963830f9.js";let t=new o,s=location.origin+"/";const n=e=>t.get(Object.assign({},{url:s+"distribution/shareMaterial/chuangkit/init",showLoading:!0,headers:{Authorization:sessionStorage.getItem("tokenType")+" "+sessionStorage.getItem("token"),appId:sessionStorage.getItem("appId")}},e)),r=e=>t.post(Object.assign({},{url:s+"distribution/shareMaterial/chuangkit/sign",showLoading:!0,headers:{Authorization:sessionStorage.getItem("tokenType")+" "+sessionStorage.getItem("token"),appId:sessionStorage.getItem("appId")}},e)),i=e=>t.post(Object.assign({},{url:s+"distribution/myMaterial/chuangkit/syncpic",showLoading:!0,headers:{Authorization:sessionStorage.getItem("tokenType")+" "+sessionStorage.getItem("token"),appId:sessionStorage.getItem("appId")}},e)),g=e=>t.post(Object.assign({},{url:s+"h5/shareMaterial/page",showLoading:!0,headers:{Authorization:sessionStorage.getItem("tokenType")+" "+sessionStorage.getItem("token")}},e)),c=e=>t.get(Object.assign({},{url:s+"h5/material/category/queryTopCategory",showLoading:!1,headers:{Authorization:sessionStorage.getItem("tokenType")+" "+sessionStorage.getItem("token")}},e)),u=e=>t.get(Object.assign({},{url:s+"h5/act/getLotteryActInfoById",showLoading:!1},e)),l=e=>t.get(Object.assign({},{url:s+"h5/act/getLotteryActInfoById_preivew",showLoading:!1},e)),d=e=>t.get(Object.assign({},{url:s+"h5/act/lottery/uinfo",showLoading:!1},e)),m=e=>t.get(Object.assign({},{url:s+"h5/act/lottery/goLottery",showLoading:!1},e)),h=e=>t.get(Object.assign({},{url:s+"h5/act/blackList",showLoading:!1},e)),b=e=>t.get(Object.assign({},{url:s+"presale-api/boost/list/count",showLoading:!1},e)),p=e=>t.get(Object.assign({},{url:s+"presale-api/boost/code/detail",showLoading:!1},e)),L=e=>t.get(Object.assign({},{url:s+"presale-api/boost/list",showLoading:!1},e)),f=e=>t.get(Object.assign({},{url:s+"presale-api/boost/limitation",showLoading:!1},e)),w=e=>t.get(Object.assign({},{url:s+"presale-api/boost/usage",showLoading:!1},e)),y=e=>t.post(Object.assign({},{url:s+"h5/tslink/groupInvite/getGroupInviteShopList",showLoading:!0,headers:{Authorization:sessionStorage.getItem("tokenType")+" "+sessionStorage.getItem("token")}},e)),O=e=>t.get(Object.assign({},{url:s+"h5/community/author/info",showLoading:!1},e)),j=e=>t.get(Object.assign({},{url:s+"app-community/community/author/getIdentityList",showLoading:!1},e)),I=e=>t.get(Object.assign({},{url:s+"h5/community/dynamic/page",showLoading:!1},e)),k=e=>t.get(Object.assign({},{url:s+"h5/community/commodity/dynamicProduct",showLoading:!1},e)),P=e=>t.get(Object.assign({},{url:s+"h5/community/dynamic",showLoading:!1},e)),C=e=>t.post(Object.assign({},{url:s+"h5/community/relation/modify?brandCode="+e.data.brandCode,showLoading:!1},e)),S=e=>t.get(Object.assign({},{url:s+"h5/comment/productDetailParam",showLoading:!1},e)),v=e=>t.get(Object.assign({},{url:s+"h5/community/top/album/base",showLoading:!1},e)),T=e=>t.post(Object.assign({},{url:s+"h5/community/interaction/praise?brandCode="+e.data.brandCode,showLoading:!1},e)),_=e=>t.get(Object.assign({},{url:s+"h5/community/dynamic/getPraiseComment",showLoading:!1},e)),R=e=>t.post(Object.assign({},{url:s+"h5/comment/bindDistributor",showLoading:!1},e)),x=e=>t.get(Object.assign({},{url:s+"h5/comment/hot",showLoading:!1},e)),q=e=>t.get(Object.assign({},{url:s+"h5/comment/page",showLoading:!1},e)),A=e=>t.get(Object.assign({},{url:s+"h5/comment/page/child",showLoading:!1},e)),D=e=>t.post(Object.assign({},{url:s+"h5/comment/reply?brandCode="+e.data.brandCode,showLoading:!1},e)),U=e=>t.post(Object.assign({},{url:s+"h5/community/vote/votedSingleOption",showLoading:!1},e)),V=e=>t.post(Object.assign({},{url:s+"wxmall/upload/image/ossNew",showLoading:!1},e)),z=e=>t.post(Object.assign({},{url:s+"h5/common/feedback/submitPropose",showLoading:!1},e)),B=e=>t.get(Object.assign({},{url:s+"member/checkToken",showLoading:!1},e)),E=e=>t.get(Object.assign({},{url:s+"member/exchange/orderRecord",showLoading:!1},e)),G=e=>t.get(Object.assign({},{url:s+"member/centerDataV1?advanceVisit=true",showLoading:!1},e)),M=e=>t.get(Object.assign({},{url:s+"member/geeTest/common/register",showLoading:!1},e)),Q=e=>t.post(Object.assign({},{url:s+"member/session_login/sendSms",showLoading:!1},e)),Y=e=>t.post(Object.assign({},{url:s+"member/session_login/to_login_or_register",showLoading:!1},e)),N=e=>t.post(Object.assign({},{url:s+"member/giftPack/exchange/ocrVerificationCode",showLoading:!1},e)),H=e=>t.get(Object.assign({},{url:s+"h5/v2/apply/getPrivacyInfo",showLoading:!1},e)),J=e=>t.get(Object.assign({},{url:s+"member/geeTest/common/register",showLoading:!1},e)),F=e=>t.get(Object.assign({},{url:s+"member/giftPack/product/qryDiscountCoupons",showLoading:!1},e)),K=e=>t.post(Object.assign({},{url:s+"member/giftPack/exchange/queryCustomerCouponExchangeRecord",showLoading:!1},e)),W=e=>t.post(Object.assign({},{url:s+"h5/tslink/kcQrcode/getQrcode",showLoading:!1},e)),X=e=>t.post(Object.assign({},{url:s+"member/memberweek/coupon/couponReceiveStatus",showLoading:!1},e)),Z=e=>t.post(Object.assign({},{url:s+"member/memberweek/coupon/list",showLoading:!1},e)),$=e=>t.post(Object.assign({},{url:s+"member/memberweek/coupon/claimCoupon",showLoading:!1},e)),ee=e=>t.get(Object.assign({},{url:s+"member/levelUp/view/levelConfig",showLoading:!1},e)),te=e=>t.get(Object.assign({},{url:s+"member/levelUp/view/detailList",showLoading:!1},e)),se=e=>t.get(Object.assign({},{url:s+"member/levelUp/userReceiveGiftItem",showLoading:!1},e)),oe=e=>t.get(Object.assign({},{url:s+"member/giftPack/product/seasonCardTopic",showLoading:!1},e)),ae=e=>t.get(Object.assign({},{url:s+"member/giftPack/order/seasonCardPayResult",showLoading:!1},e)),ne=e=>t.get(Object.assign({},{url:s+"member/giftPack/exchange/queryExchangeResult",showLoading:!1},e)),re=e=>t.get(Object.assign({},{url:s+"member/sales/order/queryPremiumLevelOpenStatus",showLoading:!1},e)),ie=e=>t.get(Object.assign({},{url:s+"h5/protocol/getPrivacyContent",showLoading:!1},e)),ge=e=>t.get(Object.assign({},{url:s+"h5/yellowPage/getShopYellowPageList",showLoading:!1},e)),ce=e=>t.get(Object.assign({},{url:s+"h5/yellowPage/generateToken",showLoading:!1},e)),ue=e=>t.get(Object.assign({},{url:s+"h5/yellowPage/getShopYellowPage",showLoading:!1},e));export{ie as $,C as A,D as B,y as C,V as D,O as E,I as F,k as G,v as H,_ as I,j as J,F as K,J as L,N as M,K as N,B as O,z as P,E as Q,H as R,W as S,X as T,Z as U,$ as V,ee as W,te as X,se as Y,oe as Z,G as _,g as a,re as a0,ae as a1,ne as a2,ge as a3,ce as a4,ue as a5,L as b,r as c,b as d,i as e,h as f,n as g,l as h,u as i,d as j,m as k,f as l,p as m,R as n,P as o,S as p,c as q,M as r,Q as s,Y as t,w as u,U as v,x as w,q as x,A as y,T as z};
