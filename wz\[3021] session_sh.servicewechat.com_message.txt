POST /ob/sdkmonitor?devtools_ignore=true HTTP/1.1
Host: sh.servicewechat.com
Connection: keep-alive
Content-Length: 1129
x-wx-ob-project-key: wx71a6af1f91734f18
x-wx-ob-env: appservice
xweb_xhr: 1
x-wx-ob-sdkversion: 3.8.12
x-wx-ob-pack-version: 0
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
Content-Type: application/json
Accept: */*
Sec-Fetch-Site: same-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wx71a6af1f91734f18/570/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9

{"report_items":[{"session_id":"202507312052/52c5e-0938-94f2-e3d9-56d61","sdk_version":"3.8.12 (2025.7.28 18:52:40)","metric_type":31,"metric_value":7,"os_name":"Windows 10 x64","os_version":"release"},{"session_id":"202507312052/52c5e-0938-94f2-e3d9-56d61","sdk_version":"3.8.12 (2025.7.28 18:52:40)","metric_type":68,"metric_value":1,"os_name":"Windows 10 x64","os_version":"release"},{"session_id":"202507312052/52c5e-0938-94f2-e3d9-56d61","sdk_version":"3.8.12 (2025.7.28 18:52:40)","metric_type":78,"metric_value":1,"os_name":"Windows 10 x64","os_version":"release"},{"session_id":"202507312052/52c5e-0938-94f2-e3d9-56d61","sdk_version":"3.8.12 (2025.7.28 18:52:40)","metric_type":87,"metric_value":624,"os_name":"Windows 10 x64","os_version":"release"},{"session_id":"202507312052/52c5e-0938-94f2-e3d9-56d61","sdk_version":"3.8.12 (2025.7.28 18:52:40)","metric_type":3,"metric_value":1,"os_name":"Windows 10 x64","os_version":"release"},{"session_id":"202507312052/52c5e-0938-94f2-e3d9-56d61","sdk_version":"3.8.12 (2025.7.28 18:52:40)","metric_type":2,"metric_value":32,"os_name":"Windows 10 x64","os_version":"release"}]}

HTTP/1.1 200 OK
Content-Type: application/json; charset=UTF-8
Cache-Control: no-cache, must-revalidate
RetKey: 14
LogicRet: 0
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET,HEAD,PUT,PATCH,POST,DELETE
Access-Control-Allow-Headers: content-type, x-wx-ob-source, x-wx-ob-env, x-wx-ob-sdkversion, x-wx-ob-pack-version, x-wx-ob-project-key, x-wx-ob-object-prefix
Access-Control-Max-Age: 600
Connection: keep-alive
Content-Length: 23

{"base_resp":{"ret":0}}