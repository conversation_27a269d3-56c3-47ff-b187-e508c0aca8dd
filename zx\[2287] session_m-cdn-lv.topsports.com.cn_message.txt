GET /m/assets/index.c3fdf902.css h2
host: m-cdn-lv.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: text/css,*/*;q=0.1
sec-fetch-site: same-site
sec-fetch-mode: no-cors
sec-fetch-dest: style
referer: https://m.topsports.com.cn/
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
cookie: Authorization=582b751e-135f-433c-abbb-7e9e131584ac
cookie: appletsSource=wx71a6af1f91734f18
cookie: memberId=8afbd81998600043019860526ea71303
cookie: version=4.5.2
priority: u=0



h2 200
server: Tengine
content-type: text/css
content-length: 1775
date: Thu, 31 Jul 2025 12:06:36 GMT
x-trace-id: 123-9029-1753963596173-6338700
last-modified: Thu, 31 Jul 2025 08:25:34 GMT
etag: "688b287e-6ef"
accept-ranges: bytes
via: cache19.l2cn8116[0,0,200-0,H], cache10.l2cn8116[2,0], cache10.l2cn8116[2,0], cache12.cn7330[0,0,200-0,H], cache14.cn7330[9,0]
age: 623
ali-swift-global-savetime: 1753963596
x-cache: HIT TCP_MEM_HIT dirn:-2:-2
x-swift-savetime: Thu, 31 Jul 2025 12:16:27 GMT
x-swift-cachetime: 1385
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f2217539642195088159e

:root{--van-switch-size: 30px;--van-switch-width: 2em;--van-switch-height: 1em;--van-switch-node-size: 1em;--van-switch-node-background-color: var(--van-white);--van-switch-node-box-shadow: 0 3px 1px 0 rgba(0, 0, 0, .05), 0 2px 2px 0 rgba(0, 0, 0, .1), 0 3px 3px 0 rgba(0, 0, 0, .05);--van-switch-background-color: var(--van-background-color-light);--van-switch-on-background-color: var(--van-primary-color);--van-switch-transition-duration: var(--van-animation-duration-base);--van-switch-disabled-opacity: var(--van-disabled-opacity);--van-switch-border: var(--van-border-width-base) solid rgba(0, 0, 0, .1)}.van-switch{position:relative;display:inline-block;box-sizing:content-box;width:var(--van-switch-width);height:var(--van-switch-height);font-size:var(--van-switch-size);background:var(--van-switch-background-color);border:var(--van-switch-border);border-radius:var(--van-switch-node-size);cursor:pointer;transition:background-color var(--van-switch-transition-duration)}.van-switch__node{position:absolute;top:0;left:0;width:var(--van-switch-node-size);height:var(--van-switch-node-size);font-size:inherit;background:var(--van-switch-node-background-color);border-radius:100%;box-shadow:var(--van-switch-node-box-shadow);transition:transform var(--van-switch-transition-duration) cubic-bezier(.3,1.05,.4,1.05)}.van-switch__loading{top:25%;left:25%;width:50%;height:50%;line-height:1}.van-switch--on{background:var(--van-switch-on-background-color)}.van-switch--on .van-switch__node{transform:translate(calc(var(--van-switch-width) - var(--van-switch-node-size)))}.van-switch--on .van-switch__loading{color:var(--van-switch-on-background-color)}.van-switch--disabled{cursor:not-allowed;opacity:var(--van-switch-disabled-opacity)}.van-switch--loading{cursor:default}
