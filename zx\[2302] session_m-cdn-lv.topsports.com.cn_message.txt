GET /m/assets/api_memberweek.ef769ed7.js h2
host: m-cdn-lv.topsports.com.cn
origin: https://m.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: */*
sec-fetch-site: same-site
sec-fetch-mode: cors
sec-fetch-dest: script
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1



h2 200
server: Tengine
content-type: application/javascript; charset=utf-8
content-length: 3808
date: Thu, 31 Jul 2025 12:13:19 GMT
x-trace-id: 123-7087-1753963999703-6551882
accept-ranges: bytes
via: cache19.l2cn8116[0,0,304-0,H], cache34.l2cn8116[1,0], cache34.l2cn8116[2,0], cache7.cn7330[0,0,200-0,H], cache10.cn7330[2,0]
last-modified: Thu, 31 Jul 2025 10:29:38 GMT
etag: "688b4592-ee0"
age: 220
ali-swift-global-savetime: 1753963999
x-cache: HIT TCP_MEM_HIT dirn:-2:-2
x-swift-savetime: Thu, 31 Jul 2025 12:16:27 GMT
x-swift-cachetime: 640
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f1e17539642199032652e

import{R as n}from"./index.963830f9.js";let t=new n,r="/",s=/m-test/.test(window.location.origin)?"https://m-test-cdn.topsports.com.cn/":"https://m-cdn.topsports.com.cn/";const a=e=>t.get(Object.assign({},{url:r+"h5/distribution/getDistributorInfo"},e)),i=e=>t.get(Object.assign({},{url:s+"h5/memberWeek/venue/info",showLoading:!1},e)),c=e=>t.get(Object.assign({},{url:s+"h5/topic/getTopicByPage",showLoading:!1},e)),u=e=>t.get(Object.assign({},{url:r+"h5/memberWeek/product/couponStatusList",showLoading:!1},e)),g=e=>t.get(Object.assign({},{url:s+"h5/memberWeek/product/productList",showLoading:!1},e)),m=e=>t.post(Object.assign({},{url:r+"h5/memberWeek/product/receiveCoupon",showLoading:!0,headers:{Authorization:sessionStorage.getItem("tokenType")+" "+sessionStorage.getItem("token")}},e)),b=e=>t.post(Object.assign({},{url:r+"h5/distribution/week/createShare",showLoading:!1},e)),d=e=>t.get({url:r+"member/premium/getOrderCount",...e,showLoading:!1}),l=e=>t.get({url:r+"store/member/premium/price",...e,showLoading:!1}),f=e=>t.get({url:r+"member/checkToken",...e,showLoading:!1}),p=e=>t.get(Object.assign({},{url:r+"h5/intPointAct/getBoostinstruction"},e)),h=e=>t.get(Object.assign({},{url:r+"member/sales/order/buyRecord"},e)),O=e=>t.post(Object.assign({},{url:r+"member/giftPack/order/seasonCardBuyRecord"},e)),j=e=>t.get(Object.assign({},{url:r+"member/premiumMember/receiveAward"},e)),k=e=>t.get(Object.assign({},{url:r+"member/equity/channel/classification",showLoading:!1},e)),y=e=>t.post(Object.assign({},{url:r+"member/sales/order/refund"},e)),L=e=>t.post(Object.assign({},{url:r+"member/giftPack/refundOrder/create"},e)),w=e=>t.get(Object.assign({},{url:r+"h5/act/freeOrder/actInfo"},e)),I=e=>t.get(Object.assign({},{url:r+"h5/act/freeOrder/recommendCommodity"},e)),C=e=>t.get(Object.assign({},{url:r+"h5/act/freeOrder/pageJoinRecordList"},e)),P=e=>t.post(Object.assign({},{url:r+"h5/act/freeOrder/receiveAward"},e)),R=e=>t.get(Object.assign({},{url:r+"member/giftPack/order/getGiftPackOrderList"},e)),q=e=>t.get(Object.assign({},{url:r+"h5/memberWeek/2024/subPage/info"},e)),W=e=>t.post(Object.assign({},{url:r+"member/premium/queryData"},e)),A=e=>t.get(Object.assign({},{url:r+"member/sales/order/query"},e)),S=e=>t.get(Object.assign({},{url:r+"member/99market/baseinfo"},e)),v=e=>t.get(Object.assign({},{url:r+"wxmall/members/memberBasicInfo",headers:{Authorization:sessionStorage.getItem("tokenType")+" "+sessionStorage.getItem("token")}},e)),B=e=>t.post(Object.assign({},{url:s+"member/sales/order/refundRegister"},e)),D=e=>t.get(Object.assign({},{url:s+"h5/memberWeek/2024/mainPage/info"},e)),G=e=>t.get(Object.assign({},{url:r+"h5/memberWeek/2024/mainPage/rollLampList"},e)),M=e=>t.get(Object.assign({},{url:r+"h5/memberWeek/2024/mainPage/6999CardInfo"},e)),T=e=>t.get(Object.assign({},{url:r+"h5/memberWeek/2024/mainPage/offline/getLotteryActInfoById"},e)),x=e=>t.get(Object.assign({},{url:r+"h5/memberWeek/2024/mainPage/offline/goLottery"},e)),z=e=>t.get(Object.assign({},{url:r+"h5/memberWeek/2024/mainPage/daShenCardInfo"},e)),E=e=>t.post(Object.assign({},{url:s+"h5/act/subscribeReminder"},e)),H=e=>t.post(Object.assign({},{url:r+"member/premium/subscribe/remind/insert"},e)),J=e=>t.get(Object.assign({},{url:r+"h5/memberWeek/2024/mainPage/whiteList"},e)),N=e=>t.get(Object.assign({},{url:r+"store/equityCenterController/queryEquityCategoryNew"},e)),U=e=>t.get(Object.assign({},{url:r+"member/equity/channel/product"},e)),_=e=>t.get(Object.assign({},{url:r+"member/premiumMember/couponDetail"},e));export{C as A,I as B,q as C,R as D,p as E,T as F,x as G,W as H,H as I,k as J,_ as K,N as L,U as M,j as N,A as a,d as b,b as c,l as d,M as e,f,a as g,S as h,D as i,g as j,u as k,m as l,h as m,O as n,v as o,c as p,z as q,G as r,E as s,B as t,L as u,i as v,J as w,y as x,w as y,P as z};
