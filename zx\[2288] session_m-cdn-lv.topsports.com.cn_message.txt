GET /m/assets/index.dbd0cbcb.js h2
host: m-cdn-lv.topsports.com.cn
origin: https://m.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: */*
sec-fetch-site: same-site
sec-fetch-mode: cors
sec-fetch-dest: script
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1



h2 200
server: Tengine
content-type: application/javascript; charset=utf-8
content-length: 6902
date: Thu, 31 Jul 2025 12:13:19 GMT
x-trace-id: 123-7087-1753963999817-6551902
accept-ranges: bytes
via: cache4.l2cn8116[0,0,304-0,H], cache9.l2cn8116[1,0], cache9.l2cn8116[1,0], cache4.cn7330[0,0,200-0,H], cache10.cn7330[1,0]
last-modified: Thu, 31 Jul 2025 08:25:34 GMT
etag: "688b287e-1af6"
age: 220
ali-swift-global-savetime: 1753963999
x-cache: HIT TCP_MEM_HIT dirn:-2:-2
x-swift-savetime: Thu, 31 Jul 2025 12:16:27 GMT
x-swift-cachetime: 1385
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f1e17539642195131470e

import{g as H,f as Y,ab as N,ac as G,U as Q,i as o,E as T,d as O,ad as X,ae as E,W as p,J as v,O as x,N as L,af as Z,ag as ee,ah as ne,ai as te,aj as oe,H as z,ak as ae,al as le,am as ie,an as se,r as re,ao as q,ap as ce,aq as ue,ar as de,y as fe,as as F,at as ge,au as me,av as Be,aw as he,A as be,ax as ve,ay as Ce}from"./index.963830f9.js";import{r as M,u as W}from"./use-route.69b0f6c5.js";const ye=(e,i)=>{const n=H(),l=()=>{n.value=Q(e).height};return Y(()=>{if(N(l),i)for(let s=1;s<=3;s++)setTimeout(l,100*s)}),G(()=>N(l)),n};function we(e,i){const n=ye(e,!0);return l=>o("div",{class:i("placeholder"),style:{height:n.value?`${n.value}px`:void 0}},[l()])}const[j,K]=T("action-bar"),J=Symbol(j),xe={placeholder:Boolean,safeAreaInsetBottom:E};var Pe=O({name:j,props:xe,setup(e,{slots:i}){const n=H(),l=we(n,K),{linkChildren:s}=X(J);s();const g=()=>{var h;return o("div",{ref:n,class:[K(),{"van-safe-area-bottom":e.safeAreaInsetBottom}]},[(h=i.default)==null?void 0:h.call(i)])};return()=>e.placeholder?l(g):g()}});const Se=p(Pe),[ke,w]=T("button"),Te=v({},M,{tag:x("button"),text:String,icon:String,type:x("default"),size:x("normal"),color:String,block:Boolean,plain:Boolean,round:Boolean,square:Boolean,loading:Boolean,hairline:Boolean,disabled:Boolean,iconPrefix:String,nativeType:x("button"),loadingSize:L,loadingText:String,loadingType:String,iconPosition:x("left")});var Oe=O({name:ke,props:Te,emits:["click"],setup(e,{emit:i,slots:n}){const l=W(),s=()=>n.loading?n.loading():o(te,{size:e.loadingSize,type:e.loadingType,class:w("loading")},null),g=()=>{if(e.loading)return s();if(n.icon)return o("div",{class:w("icon")},[n.icon()]);if(e.icon)return o(ee,{name:e.icon,class:w("icon"),classPrefix:e.iconPrefix},null)},h=()=>{let a;if(e.loading?a=e.loadingText:a=n.default?n.default():e.text,a)return o("span",{class:w("text")},[a])},u=()=>{const{color:a,plain:b}=e;if(a){const m={color:b?a:"white"};return b||(m.background=a),a.includes("gradient")?m.border=0:m.borderColor=a,m}},C=a=>{e.loading?ne(a):e.disabled||(i("click",a),l())};return()=>{const{tag:a,type:b,size:m,block:P,round:D,plain:R,square:$,loading:_,disabled:t,hairline:c,nativeType:B,iconPosition:d}=e,y=[w([b,m,{plain:R,block:P,round:D,square:$,loading:_,disabled:t,hairline:c}]),{[Z]:c}];return o(a,{type:B,class:y,style:u(),disabled:t,onClick:C},{default:()=>[o("div",{class:w("content")},[d==="left"&&g(),h(),d==="right"&&g()])]})}}});const I=p(Oe),[pe,De]=T("action-bar-button"),Re=v({},M,{type:String,text:String,icon:String,color:String,loading:Boolean,disabled:Boolean});var $e=O({name:pe,props:Re,setup(e,{slots:i}){const n=W(),{parent:l,index:s}=oe(J),g=z(()=>{if(l){const u=l.children[s.value-1];return!(u&&"isButton"in u)}}),h=z(()=>{if(l){const u=l.children[s.value+1];return!(u&&"isButton"in u)}});return ae({isButton:!0}),()=>{const{type:u,icon:C,text:a,color:b,loading:m,disabled:P}=e;return o(I,{class:De([u,{last:h.value,first:g.value}]),size:"large",type:u,icon:C,color:b,loading:m,disabled:P,onClick:n},{default:()=>[i.default?i.default():a]})}}});const U=p($e),[_e,f,k]=T("dialog"),Ae=v({},le,{title:String,theme:String,width:L,message:[String,Function],callback:Function,allowHtml:Boolean,className:ie,transition:x("van-dialog-bounce"),messageAlign:String,closeOnPopstate:E,showCancelButton:Boolean,cancelButtonText:String,cancelButtonColor:String,cancelButtonDisabled:Boolean,confirmButtonText:String,confirmButtonColor:String,confirmButtonDisabled:Boolean,showConfirmButton:E,closeOnClickOverlay:Boolean}),Ee=[...se,"transition","closeOnPopstate"];var V=O({name:_e,props:Ae,emits:["confirm","cancel","keydown","update:show"],setup(e,{emit:i,slots:n}){const l=H(),s=re({confirm:!1,cancel:!1}),g=t=>i("update:show",t),h=t=>{var c;g(!1),(c=e.callback)==null||c.call(e,t)},u=t=>()=>{!e.show||(i(t),e.beforeClose?(s[t]=!0,he(e.beforeClose,{args:[t],done(){h(t),s[t]=!1},canceled(){s[t]=!1}})):h(t))},C=u("cancel"),a=u("confirm"),b=fe(t=>{var c,B;if(t.target!==((B=(c=l.value)==null?void 0:c.popupRef)==null?void 0:B.value))return;({Enter:e.showConfirmButton?a:F,Escape:e.showCancelButton?C:F})[t.key](),i("keydown",t)},["enter","esc"]),m=()=>{const t=n.title?n.title():e.title;if(t)return o("div",{class:f("header",{isolated:!e.message&&!n.default})},[t])},P=t=>{const{message:c,allowHtml:B,messageAlign:d}=e,y=f("message",{"has-title":t,[d]:d}),A=ge(c)?c():c;return B&&typeof A=="string"?o("div",{class:y,innerHTML:A},null):o("div",{class:y},[A])},D=()=>{if(n.default)return o("div",{class:f("content")},[n.default()]);const{title:t,message:c,allowHtml:B}=e;if(c){const d=!!(t||n.title);return o("div",{key:B?1:0,class:f("content",{isolated:!d})},[P(d)])}},R=()=>o("div",{class:[Be,f("footer")]},[e.showCancelButton&&o(I,{size:"large",text:e.cancelButtonText||k("cancel"),class:f("cancel"),style:{color:e.cancelButtonColor},loading:s.cancel,disabled:e.cancelButtonDisabled,onClick:C},null),e.showConfirmButton&&o(I,{size:"large",text:e.confirmButtonText||k("confirm"),class:[f("confirm"),{[me]:e.showCancelButton}],style:{color:e.confirmButtonColor},loading:s.confirm,disabled:e.confirmButtonDisabled,onClick:a},null)]),$=()=>o(Se,{class:f("footer")},{default:()=>[e.showCancelButton&&o(U,{type:"warning",text:e.cancelButtonText||k("cancel"),class:f("cancel"),color:e.cancelButtonColor,loading:s.cancel,disabled:e.cancelButtonDisabled,onClick:C},null),e.showConfirmButton&&o(U,{type:"danger",text:e.confirmButtonText||k("confirm"),class:f("confirm"),color:e.confirmButtonColor,loading:s.confirm,disabled:e.confirmButtonDisabled,onClick:a},null)]}),_=()=>n.footer?n.footer():e.theme==="round-button"?$():R();return()=>{const{width:t,title:c,theme:B,message:d,className:y}=e;return o(de,q({ref:l,role:"dialog",class:[f([B]),y],style:{width:ue(t)},tabindex:0,"aria-labelledby":c||d,onKeydown:b,"onUpdate:show":g},ce(e,Ee)),{default:()=>[m(),D(),_()]})}}});let S;function Ie(){({instance:S}=ve({setup(){const{state:i,toggle:n}=Ce();return()=>o(V,q(i,{"onUpdate:show":n}),null)}}))}function r(e){return be?new Promise((i,n)=>{S||Ie(),S.open(v({},r.currentOptions,e,{callback:l=>{(l==="confirm"?i:n)(l)}}))}):Promise.resolve()}r.defaultOptions={title:"",width:"",theme:null,message:"",overlay:!0,callback:null,teleport:"body",className:"",allowHtml:!1,lockScroll:!0,transition:void 0,beforeClose:null,overlayClass:"",overlayStyle:void 0,messageAlign:"",cancelButtonText:"",cancelButtonColor:null,cancelButtonDisabled:!1,confirmButtonText:"",confirmButtonColor:null,confirmButtonDisabled:!1,showConfirmButton:!0,showCancelButton:!1,closeOnPopstate:!0,closeOnClickOverlay:!1};r.currentOptions=v({},r.defaultOptions);r.alert=r;r.confirm=e=>r(v({showCancelButton:!0},e));r.close=()=>{S&&S.toggle(!1)};r.setDefaultOptions=e=>{v(r.currentOptions,e)};r.resetDefaultOptions=()=>{r.currentOptions=v({},r.defaultOptions)};r.Component=p(V);r.install=e=>{e.use(r.Component),e.config.globalProperties.$dialog=r};export{r as D};
