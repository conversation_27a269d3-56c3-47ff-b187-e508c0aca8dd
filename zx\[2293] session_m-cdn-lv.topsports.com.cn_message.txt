GET /m/assets/Mission.ac64c021.js h2
host: m-cdn-lv.topsports.com.cn
origin: https://m.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: */*
sec-fetch-site: same-site
sec-fetch-mode: cors
sec-fetch-dest: script
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1



h2 200
server: Tengine
content-type: application/javascript; charset=utf-8
content-length: 129330
date: Thu, 31 Jul 2025 12:13:19 GMT
x-trace-id: 123-70109-1753963999786-6461929
last-modified: Thu, 31 Jul 2025 10:28:26 GMT
etag: "688b454a-1f932"
accept-ranges: bytes
via: cache11.l2cn8116[0,0,200-0,H], cache5.l2cn8116[1,0], cache5.l2cn8116[1,0], cache3.cn7330[0,0,200-0,H], cache10.cn7330[3,0]
age: 220
ali-swift-global-savetime: 1753963999
x-cache: HIT TCP_MEM_HIT dirn:-2:-2
x-swift-savetime: Thu, 31 Jul 2025 12:16:27 GMT
x-swift-cachetime: 648
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f1e17539642195551609e

import{_ as R,d as q,o as d,c as p,i as E,w as D,v as M,b as A,X as F,z as b,a1 as L,a2 as E0,u as Q0,g,f as l0,C as g0,a5 as B0,$ as c0,e as y0,Y as n,k as X,T as N,a as G,F as A0,j as n0,n as K,p as P0,m as I0,q as q0,r as W0,a4 as Z0,a0 as S,t as _0,h as $0,l as D0,s as V,Z as e0,b9 as ss,a7 as es,bb as k0,ab as p0,aD as As,b4 as as}from"./index.963830f9.js";import{g as ts,d as os,r as w0,a as is,b as rs}from"./api_mission.16fe0a4f.js";import{O as ns}from"./api.5870f003.js";import{l as ls}from"./lottie.4c88a68e.js";import{g as m0}from"./index.c6ddc126.js";const gs={class:"miss-dialog-container"},Bs=["onTouchmove"],cs={class:"miss-dialog-content"},ds=q({__name:"MissionDialog",props:{show:{type:Boolean,required:!0}},emits:["closeDialog"],setup(i,{emit:c}){const w=C=>{C.stopPropagation(),C.preventDefault()},x=()=>{c("closeDialog")};return(C,P)=>(d(),p("div",gs,[E(L,{name:"mmask",onClick:x,persisted:""},{default:D(()=>[M(A("div",{class:"miss-dialog-mask",onTouchmove:F(w,["stop"])},null,40,Bs),[[b,i.show]])]),_:1}),E(L,{name:"mmask",persisted:""},{default:D(()=>[M(A("div",cs,[E0(C.$slots,"default",{},void 0,!0)],512),[[b,i.show]])]),_:3})]))}});var fs=R(ds,[["__scopeId","data-v-4bbda39e"],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/components/mission/MissionDialog.vue"]]);let xs=0;const Cs=()=>{window.cancelAnimationFrame(xs)},us="4.8.0",hs={g:"LottieFiles AE ",a:"",k:"",d:"",tc:""},Ds=60,ks=0,ps=301,ws=512,ms=512,vs="Yboo-light-efffect",Es=0,Qs=[{id:"image_0",w:512,h:512,u:"",p:"data:image/png;base64,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",e:1}],ys=[{ddd:0,ind:1,ty:4,nm:"star 20",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:132,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:162,s:[90]},{t:222,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:132,s:[0]},{t:222,s:[360]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:132,s:[256,256,0],to:[-32.667,31.833,0],ti:[32.667,-31.833,0]},{t:222,s:[60,447,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:132,s:[8.062,8.062,100]},{t:222,s:[12.062,12.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:132,op:732,st:132,bm:0},{ddd:0,ind:2,ty:4,nm:"star 19",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:147,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:177,s:[90]},{t:237,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:147,s:[0]},{t:237,s:[405]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:147,s:[256,256,0],to:[29.333,29,0],ti:[-29.333,-29,0]},{t:237,s:[432,430,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:147,s:[8.062,8.062,100]},{t:237,s:[17.062,17.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:147,op:747,st:147,bm:0},{ddd:0,ind:3,ty:4,nm:"star 18",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:162,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:192,s:[90]},{t:222,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:162,s:[0]},{t:222,s:[405]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:162,s:[256,256,0],to:[29,-27.5,0],ti:[-29,27.5,0]},{t:222,s:[430,91,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:162,s:[8.062,8.062,100]},{t:222,s:[5.062,5.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:162,op:762,st:162,bm:0},{ddd:0,ind:4,ty:4,nm:"star 17",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:180,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:210,s:[90]},{t:270,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:180,s:[14]},{t:270,s:[405]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:180,s:[256,256,0],to:[-7.333,-30.667,0],ti:[7.333,30.667,0]},{t:270,s:[212,72,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:180,s:[8.062,8.062,100]},{t:270,s:[13.062,13.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:180,op:780,st:180,bm:0},{ddd:0,ind:5,ty:4,nm:"star 16",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:140,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:170,s:[90]},{t:230,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:140,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:230,s:[405]},{t:231,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:140,s:[256,256,0],to:[-32.333,-15,0],ti:[32.333,15,0]},{t:230,s:[62,166,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:140,s:[8.062,8.062,100]},{t:230,s:[7.063,7.063,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:140,op:740,st:140,bm:0},{ddd:0,ind:6,ty:4,nm:"star 15",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:164,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:194,s:[90]},{t:239,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:164,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:239,s:[364]},{t:240,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:164,s:[256,256,0],to:[-31.167,8.667,0],ti:[31.167,-8.667,0]},{t:239,s:[69,308,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:164,s:[8.062,8.062,100]},{t:239,s:[12.062,12.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:164,op:764,st:164,bm:0},{ddd:0,ind:7,ty:4,nm:"star 14",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:175,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:205,s:[90]},{t:265,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:175,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:265,s:[453]},{t:266,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:175,s:[256,256,0],to:[11,30.333,0],ti:[-11,-30.333,0]},{t:265,s:[322,438,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:175,s:[8.062,8.062,100]},{t:265,s:[12.062,12.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:175,op:775,st:175,bm:0},{ddd:0,ind:8,ty:4,nm:"star 13",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:190,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:220,s:[90]},{t:280,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:190,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:280,s:[453]},{t:281,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:190,s:[256,256,0],to:[34.667,15.667,0],ti:[-34.667,-15.667,0]},{t:280,s:[464,350,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:190,s:[8.062,8.062,100]},{t:280,s:[5.062,5.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:190,op:790,st:190,bm:0},{ddd:0,ind:9,ty:4,nm:"star 12",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:198,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:228,s:[90]},{t:288,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:198,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:288,s:[394]},{t:289,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:198,s:[256,256,0],to:[26.667,-2.833,0],ti:[-26.667,2.833,0]},{t:288,s:[416,239,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:198,s:[8.062,8.062,100]},{t:288,s:[13.062,13.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:198,op:798,st:198,bm:0},{ddd:0,ind:10,ty:4,nm:"star 11",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:151,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:181,s:[90]},{t:271,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:151,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:271,s:[381]},{t:272,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:151,s:[256,256,0],to:[16.333,-28.333,0],ti:[-16.333,28.333,0]},{t:271,s:[354,86,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:151,s:[8.062,8.062,100]},{t:271,s:[13.062,13.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:151,op:751,st:151,bm:0},{ddd:0,ind:11,ty:4,nm:"star 10",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:19,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:49,s:[90]},{t:139,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:19,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:139,s:[381]},{t:140,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:19,s:[256,256,0],to:[16.333,-28.333,0],ti:[-16.333,28.333,0]},{t:139,s:[354,86,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:19,s:[8.062,8.062,100]},{t:139,s:[13.062,13.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:19,op:619,st:19,bm:0},{ddd:0,ind:12,ty:4,nm:"star 9",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:66,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:96,s:[90]},{t:156,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:66,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:156,s:[394]},{t:157,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:66,s:[256,256,0],to:[26.667,-2.833,0],ti:[-26.667,2.833,0]},{t:156,s:[416,239,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:66,s:[8.062,8.062,100]},{t:156,s:[13.062,13.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:66,op:666,st:66,bm:0},{ddd:0,ind:13,ty:4,nm:"star 8",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:58,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:88,s:[90]},{t:148,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:58,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:148,s:[453]},{t:149,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:58,s:[256,256,0],to:[34.667,15.667,0],ti:[-34.667,-15.667,0]},{t:148,s:[464,350,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:58,s:[8.062,8.062,100]},{t:148,s:[5.062,5.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:58,op:658,st:58,bm:0},{ddd:0,ind:14,ty:4,nm:"star 7",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:43,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:73,s:[90]},{t:133,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:43,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:133,s:[453]},{t:134,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:43,s:[256,256,0],to:[11,30.333,0],ti:[-11,-30.333,0]},{t:133,s:[322,438,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:43,s:[8.062,8.062,100]},{t:133,s:[12.062,12.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:43,op:643,st:43,bm:0},{ddd:0,ind:15,ty:4,nm:"star 6",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:32,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:62,s:[90]},{t:107,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:32,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:107,s:[364]},{t:108,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:32,s:[256,256,0],to:[-31.167,8.667,0],ti:[31.167,-8.667,0]},{t:107,s:[69,308,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:32,s:[8.062,8.062,100]},{t:107,s:[12.062,12.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:32,op:632,st:32,bm:0},{ddd:0,ind:16,ty:4,nm:"star 5",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:8,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:38,s:[90]},{t:98,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:8,s:[39]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:98,s:[405]},{t:99,s:[39]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:8,s:[256,256,0],to:[-32.333,-15,0],ti:[32.333,15,0]},{t:98,s:[62,166,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:8,s:[8.062,8.062,100]},{t:98,s:[7.063,7.063,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:8,op:608,st:8,bm:0},{ddd:0,ind:17,ty:4,nm:"star 4",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:48,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:78,s:[90]},{t:138,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:48,s:[14]},{t:138,s:[405]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:48,s:[256,256,0],to:[-7.333,-30.667,0],ti:[7.333,30.667,0]},{t:138,s:[212,72,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:48,s:[8.062,8.062,100]},{t:138,s:[13.062,13.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:48,op:648,st:48,bm:0},{ddd:0,ind:18,ty:4,nm:"star 3",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:30,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:60,s:[90]},{t:90,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:30,s:[0]},{t:90,s:[405]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:30,s:[256,256,0],to:[29,-27.5,0],ti:[-29,27.5,0]},{t:90,s:[430,91,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:30,s:[8.062,8.062,100]},{t:90,s:[5.062,5.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:30,op:630,st:30,bm:0},{ddd:0,ind:19,ty:4,nm:"star 2",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:15,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:45,s:[90]},{t:105,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:15,s:[0]},{t:105,s:[405]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:15,s:[256,256,0],to:[29.333,29,0],ti:[-29.333,-29,0]},{t:105,s:[432,430,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:15,s:[8.062,8.062,100]},{t:105,s:[17.062,17.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:15,op:615,st:15,bm:0},{ddd:0,ind:20,ty:4,nm:"star",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:0,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:30,s:[90]},{t:90,s:[0]}],ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:0,s:[0]},{t:90,s:[360]}],ix:10},p:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:0,s:[256,256,0],to:[-32.667,31.833,0],ti:[32.667,-31.833,0]},{t:90,s:[60,447,0]}],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:0,s:[8.062,8.062,100]},{t:90,s:[12.062,12.062,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0],[0,0]],v:[[0,-218.474],[68.793,-71.453],[229.82,-51.589],[111.219,59.174],[142.068,218.474],[0,139.917],[-142.068,218.474],[-111.206,59.174],[-229.82,-51.589],[-68.78,-71.453]],c:!0},ix:2},nm:"\u041A\u043E\u043D\u0442\u0443\u0440 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"\u0417\u0430\u043B\u0438\u0432\u043A\u0430 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[255.85,255.969],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"\u0413\u0440\u0443\u043F\u043F\u0430 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:600,st:0,bm:0},{ddd:0,ind:21,ty:4,nm:"Shape Layer 1",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[256,256,0],ix:2},a:{a:0,k:[29.103,48.103,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[310.206,310.206],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"st",c:{a:0,k:[1,1,1,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:0,ix:5},lc:1,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"gf",o:{a:0,k:100,ix:10},r:1,bm:0,g:{p:3,k:{a:0,k:[0,1,.993,.761,.5,1,.996,.858,1,1,.999,.954,0,1,.5,.5,1,0],ix:9}},s:{a:0,k:[0,0],ix:5},e:{a:0,k:[155.083,1.754],ix:6},t:2,h:{a:0,k:0,ix:7},a:{a:0,k:0,ix:8},nm:"Gradient Fill 2",mn:"ADBE Vector Graphic - G-Fill",hd:!1},{ty:"tr",p:{a:0,k:[29.103,48.103],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:600,st:0,bm:0},{ddd:0,ind:24,ty:2,nm:"light/light-def.ai",cl:"ai",refId:"image_0",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:0,s:[0]},{t:300,s:[360]}],ix:10},p:{a:0,k:[256,256,0],ix:2},a:{a:0,k:[256,256,0],ix:1},s:{a:0,k:[82.031,82.031,100],ix:6}},ao:0,ip:0,op:600,st:0,bm:0}],Ps=[];var v0={v:us,meta:hs,fr:Ds,ip:ks,op:ps,w:ws,h:ms,nm:vs,ddd:Es,assets:Qs,layers:ys,markers:Ps};const Is={class:"reward-dialog-container"},Hs=["onTouchmove"],Ms=["onClick"],bs=q({__name:"MissionRewardDialog",props:{show:{type:Boolean,required:!0},configData:{type:null,required:!0}},emits:["closeDialog"],setup(i,{emit:c}){const w=c0(),x=w==null?void 0:w.appContext.config.globalProperties.brandCode,C=Q0(),P=B=>{B.stopPropagation(),B.preventDefault()};console.log("shining",v0);const k=g(null),m=()=>{c("closeDialog")};l0(()=>{ls.loadAnimation({container:k.value,renderer:"svg",loop:!0,autoplay:!0,animationData:v0})}),g0(()=>{Cs()});const Q=()=>{C.push({name:"rewardcenter",query:{brandCode:x}})};return(B,v)=>(d(),p("div",Is,[E(L,{name:"mcontent",persisted:""},{default:D(()=>[M(A("div",{class:"miss-dialog-mask",onTouchmove:F(P,["stop"])},null,40,Hs),[[b,i.show]])]),_:1}),E(L,{name:"mcontent",persisted:""},{default:D(()=>[M(A("div",{class:"reward-lottie",ref_key:"anim",ref:k},null,512),[[b,i.show]])]),_:1}),E(L,{name:"mcontent",persisted:""},{default:D(()=>[M(A("div",{class:"miss-dialog-content",style:B0({backgroundImage:`url(${i.configData.taskPopupBg})`})},[E0(B.$slots,"default",{},void 0,!0),A("div",{class:"miss-dialog-btn",onClick:F(Q,["stop"])}," \u67E5\u770B\u5956\u52B1 ",8,Ms),A("span",{class:"iconfont miss-dialog-closed",onClick:m},"\uE64E")],4),[[b,i.show]])]),_:3})]))}});var Fs=R(bs,[["__scopeId","data-v-7b2faa00"],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/components/mission/MissionRewardDialog.vue"]]);const Ls={class:"share-dialog-container"},Ys=["onTouchmove"],Us={class:"arrow-text"},js=q({__name:"ShareArrow",props:{show:{type:Boolean,required:!0}},emits:["closeDialog"],setup(i,{emit:c}){const w=k=>{k.stopPropagation(),k.preventDefault()};let x=g(!1),C=g(!1);y0(()=>{x.value=window.isMiniprogramEnv,C.value=window.TopsportsAppEnv});const P=()=>{c("closeDialog")};return(k,m)=>(d(),p("div",Ls,[E(L,{name:"smask",onClick:P,persisted:""},{default:D(()=>[M(A("div",{class:"miss-dialog-mask",onTouchmove:F(w,["stop"])},null,40,Ys),[[b,i.show]])]),_:1}),E(L,{name:"scontent",persisted:""},{default:D(()=>[M(A("div",{alt:"",class:"arrow-icon",style:B0({right:n(x)?"14vw":"4vw"})},[A("div",Us," \u70B9\u51FB\u53F3\u4E0A\u89D2"+X(n(C)?"":"\u300E...\u300F")+"\u5206\u4EAB\u6B64\u9875\u9762 ",1)],4),[[b,i.show]])]),_:1})]))}});var Gs=R(js,[["__scopeId","data-v-3718c6cc"],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/components/ShareArrow.vue"]]);const Xs=i=>(P0("data-v-36914699"),i=i(),I0(),i),Ts={class:"feed-dialog-container"},Os=["onTouchmove"],zs={class:"feed-dialog-content"},Ss=Xs(()=>A("div",{class:"feed-dg-top"},[A("img",{class:"feed-logo",src:"https://member-oss-cdn.topsports.com.cn/front_end/web_image/app-logo.png",alt:""}),A("div",{class:"feed-text"},"\u559C\u6B22 \u201C\u6ED4\u535A\u8FD0\u52A8\u201D \u5417\uFF1F"),A("div",{class:"feed-sub-text"},"\u5C0F\u7F16\u7684\u9E21\u817F\u5C31\u770B\u4F60\u7684\u8BC4\u4EF7\u4E86\uFF01")],-1)),Ns={class:"feed-dg-stars"},Js=["onClick"],Vs=["onClick"],Ks=["onClick"],Rs=q({__name:"FeedDialog",props:{show:{type:Boolean,required:!0},feedCompleteUpperLimit:{type:Number,required:!0}},emits:["feedConfirm","closeDialog"],setup(i,{emit:c}){const w=i,x=Q0(),C=c0(),P=C==null?void 0:C.appContext.config.globalProperties.brandCode;let k=g(!1);const m=l=>{l.stopPropagation(),l.preventDefault()};l0(()=>{k.value=window.TopsportsAppEnv,k.value&&(window.showAppMarketScoreCallback=l=>{let f=B.value.filter(I=>I),u=!1;l?u=!0:(u=!1,N("\u8BC4\u5206\u6210\u529F"),c("feedConfirm",f.length),c("closeDialog")),window.sensors.track("ScoreClick",{score:f.length,is_success:u})},window.jumpMarketScoreCallback=l=>{let f=B.value.filter(I=>I),u=!1;l?u=!0:(u=!1,N("\u8BC4\u5206\u6210\u529F"),c("feedConfirm",f.length),c("closeDialog")),window.sensors.track("ScoreClick",{score:f.length,is_success:u})})}),g0(()=>{k.value&&(window.showAppMarketScoreCallback=null,window.jumpMarketScoreCallback=null)});const Q=()=>{c("closeDialog")};let B=g([!1,!1,!1,!1,!1]);const v=l=>{for(let f=0;f<B.value.length;f++)B.value[f]=!1,l>=f&&(B.value[f]=!0)},O=()=>{if(!window.TopsportsAppEnv)return;let l=B.value.filter(f=>f);l.length!==0&&(l.length>=4?window.isAndroid?(c("feedConfirm",l.length),c("closeDialog"),window.TopsportsApp.postMessage(JSON.stringify({method:"jumpMarketScore",data:{}}))):window.isIos&&(c("feedConfirm",l.length),c("closeDialog"),w.feedCompleteUpperLimit<3?window.TopsportsApp.postMessage(JSON.stringify({method:"showAppMarketScore",data:{}})):window.TopsportsApp.postMessage(JSON.stringify({method:"jumpMarketScore",data:{}}))):(window.sensors.track("ScoreClick",{score:l.length,is_success:!1}),c("feedConfirm",l.length),c("closeDialog"),x.push({name:"feedback",query:{brandCode:P}})))},Y=()=>{c("closeDialog",!0)};return(l,f)=>(d(),p("div",Ts,[E(L,{name:"fmask",onClick:Q,persisted:""},{default:D(()=>[M(A("div",{class:"feed-dialog-mask",onTouchmove:F(m,["stop"])},null,40,Os),[[b,i.show]])]),_:1}),E(L,{name:"kcontent",persisted:""},{default:D(()=>[M(A("div",zs,[G(' <div class="feed-dialog-filter"></div> '),Ss,A("div",Ns,[(d(!0),p(A0,null,n0(n(B),(u,I)=>(d(),p("div",{class:"iconfont feed-dg-star",key:I,onClick:F(j=>v(I),["stop"])},X(u?"\uE657":"\uE656"),9,Js))),128))]),A("div",{class:"feed-dg-btn",onClick:Q},[A("span",{class:"feed-btn-item feed-btn-first",onClick:F(Y,["stop"])},"\u53D6\u6D88",8,Vs),A("span",{class:K({"feed-btn-item":!0,"feed-btn-item-disabled":n(B).filter(u=>u).length===0}),onClick:F(O,["stop"])},"\u63D0\u4EA4",10,Ks)])],512),[[b,i.show]])]),_:1})]))}});var qs=R(Rs,[["__scopeId","data-v-36914699"],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/components/mission/FeedDialog.vue"]]);const T=i=>(P0("data-v-57a41cee"),i=i(),I0(),i),Ws={class:"miss-container"},Zs={class:"miss-head"},_s=T(()=>A("div",{class:"head-title"}," \u4EFB\u52A1\u4E2D\u5FC3 ",-1)),$s=T(()=>A("div",{class:"head-desc"}," \u4EFB\u52A1\u6BCF\u65E5\u90FD\u4F1A\u66F4\u65B0\uFF0C\u6765\u8D5A\u53D6\u66F4\u591A\u79EF\u5206\u5427\uFF5E ",-1)),se=T(()=>A("span",null,"\u5237\u65B0",-1)),ee={class:"miss-body"},Ae=["data-index"],ae=["src"],te={class:"miss-info"},oe={class:"miss-info-title"},ie=["innerHTML"],re={key:0,class:"miss-upper"},ne=T(()=>A("img",{class:"miss-step-icon",src:"https://m-cdn-lan.topsports.com.cn/fe/mp/member_images/mission-step-icon.png",alt:""},null,-1)),le={class:"miss-step-completegoal"},ge=["innerHTML"],Be={class:"miss-info-steps"},ce=T(()=>A("img",{class:"info-step-icon",src:"https://m-cdn-lan.topsports.com.cn/fe/mp/member_images/mission-suc-icon.png"},null,-1)),de=T(()=>A("span",null,"\u53EF\u5B8C\u6210",-1)),fe={class:"step-bold"},xe=["id","onClick"],Ce=T(()=>A("div",{class:"test-inner"},"1",-1)),ue={class:"miss-toggle-text"},he=["src"],De={class:"miss-reward-group"},ke=["src"],pe={class:"miss-reward-text"},we=q({__name:"Mission",props:{channelType:{type:String,required:!0},taskFoldCount:{type:Number,required:!0},configData:{type:null,required:!0}},setup(i,{expose:c,emit:w}){const x=i;let C=g(""),P=g("");const k=c0(),m=k==null?void 0:k.appContext.config.globalProperties.brandCode,Q=window.sessionStorage.getItem("minienv")||"";let B=g(!1),v=g(!1),O=g(!1),Y=g(""),l=g(""),f=g(!1),u=g(!1),I=g(!0),j=g(!1),a0=g(""),d0=g(!1),z=g(!1),W=g(!1),f0=g(0),t0=g(!1),o0="",Z="";const H0=q0("getPoints",()=>{},!1);let U=W0({missionList:[],rewardList:[]});y0(()=>{v.value=window.isMiniprogramEnv,B.value=window.TopsportsAppEnv,O.value=window.isWechat,j.value=!1,d0.value=window.location.origin.indexOf("https://m.topsports.com.cn")>=0,Z0(m,!0),m==="TS"?(Y.value="gh_c9daa25f7c4f",l.value="gh_adc53d1d8ef6",o0="wx71a6af1f91734f18"):(Y.value="gh_5517255c7eee",l.value="gh_b8b830107933",o0="wx48276da8d2bdf9a6")}),l0(()=>{M0(),$(),document.addEventListener("visibilitychange",_),document.addEventListener("resume",_)});const _=()=>{document.hidden?console.log("visiblehidden"):(console.log("visibleshow"),$())};g0(()=>{document.removeEventListener("resume",_),document.removeEventListener("visibilitychange",_)});const $=async s=>{s&&(W.value=!0);try{let{data:a}=await ts({params:{channelType:x.channelType,brandCode:m},showLoading:!1});if(W.value=!1,a.bizCode===2e4){let t={data:{brand:m,browse_page:window.location.href,channel_type:x.channelType,into_page_time:new Date().getTime()},table:"e_browse_duration_tmp"};i0(t,0);let e=a.data.list,o;(r=>{r[r.\u9886\u5956\u52B1=1]="\u9886\u5956\u52B1",r[r.\u53BB\u5B8C\u6210=2]="\u53BB\u5B8C\u6210",r[r.\u5DF2\u5B8C\u6210=3]="\u5DF2\u5B8C\u6210",r[r.\u4ECA\u65E5\u5DF2\u5B8C\u6210=301]="\u4ECA\u65E5\u5DF2\u5B8C\u6210"})(o||(o={}));for(let r=0;r<e.length;r++){let y=e[r];y.taskStatus!==2?y.taskStatusName=o[y.taskStatus]:y.taskStatusName=y.jumpDoc;let h0=x0(y);if(h0.length>0){let h=h0[0];if(O.value&&!v.value){let H="",J="";(h.jumpUrlType==2||h.jumpUrlType==3||h.jumpUrlType==5)&&(/\?/g.test(h.jumpUrlPath)?(H=h.jumpUrlPath.replace("?",".html?"),H=`${H}&taskId=${y.taskId}`):(H=h.jumpUrlPath+".html",H=`${H}?taskId=${y.taskId}`),h.jumpUrlType==2&&(J=Y.value),h.jumpUrlType==3&&(J=l.value),h.jumpUrlType==5&&(J=h.jumpAppId)),h.jumpType==3&&(H=`/userContent/pages/authCenter/index.html?type=step&taskId=${y.taskId}`,J=Y.value),y.wechatJumpMiniUrl=H,y.wechatJumpUserName=J}}else e.splice(r--,1);e.map((h,H)=>{e.length>x.taskFoldCount&&x.taskFoldCount>0&&z.value===!1&&H>x.taskFoldCount-1?h.visible=!1:h.visible=!0})}U.missionList=e}else N(a.bizMsg)}catch(a){W.value=!1,console.error(a)}},s0=async()=>{if(Z){let{data:s}=await rs({params:{channelType:x.channelType,taskId:Z,brandCode:m}});if(Z="",s.bizCode===2e4){let a;(r=>{r[r.\u9886\u5956\u52B1=1]="\u9886\u5956\u52B1",r[r.\u53BB\u5B8C\u6210=2]="\u53BB\u5B8C\u6210",r[r.\u5DF2\u5B8C\u6210=3]="\u5DF2\u5B8C\u6210",r[r.\u4ECA\u65E5\u5DF2\u5B8C\u6210=301]="\u4ECA\u65E5\u5DF2\u5B8C\u6210"})(a||(a={}));let t=s.data,e=U.missionList,o=e.findIndex(r=>r.taskId===t.taskId);t.taskStatus!==2?t.taskStatusName=a[t.taskStatus]:t.taskStatusName=t.jumpDoc,e[o]=Object.assign({},e[o],t)}}},M0=async()=>{let{data:s}=await ns({showLoading:!1});s.bizCode===20002?t0.value=!1:t0.value=!0},i0=async(s,a)=>{let{data:t}=await os({data:s,showLoading:!1});if(t.bizCode===2e4)a&&a>0&&setTimeout(()=>{s0()},a);else if(t.bizCode===59999){if(a===0)return;v.value?S.miniProgram.navigateTo({url:"/pages/auth/index?url="+encodeURIComponent(window.location.href)}):B.value?window.TopsportsApp.postMessage(JSON.stringify({method:"openLogin",data:-1})):window.location.href=`https://${window.location.host}/store/myMember_controllers/to_createSession.do?brandCode=${m}&isLogin=true&returnUrl=${encodeURIComponent(window.location.href)}`}},x0=s=>{let a=s.jumpSceneInfoList,t=X0();return a.filter(o=>o.jumpScene===t)},b0=(s,a)=>{let t={task_name:s.taskName,task_id:s.taskId,task_event_tab:s.taskEventCode,link_type:"\u8DF3\u8F6C\u9875\u9762",link_parameter:s.wechatJumpMiniUrl,parameter_description:s.wechatJumpUserName===Y.value?"\u5546\u57CE\u5C0F\u7A0B\u5E8F":s.wechatJumpUserName===l.value?"\u88C2\u53D8\u5C0F\u7A0B\u5E8F":""};w("sensorTrack",{name:"TofinishClick",sensorData:t}),w0({params:{taskId:s.taskId},showLoading:!0}).then(e=>{let o=e.data;o.bizCode===2e4?console.log("H5LaunchMiniAndReceive:success"):N(o.bizMsg)})},F0=(s,a)=>{if(s.taskStatus===3)return;if(Z=s.taskId,s.taskStatus===1){T0(s);return}let t=x0(s);w0({params:{taskId:s.taskId},showLoading:!0}).then(e=>{let o=e.data;o.bizCode===2e4?t.length>0?L0(t[0],a,s):N("\u7F51\u7EDC\u7E41\u5FD9\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5"):N(o.bizMsg)})},L0=(s,a,t)=>{let e={1:C0,2:Y0,3:U0,4:j0,5:()=>{},6:G0},o={task_name:t.taskName,task_id:t.taskId,task_event_tab:t.taskEventCode,link_type:"",link_parameter:"",parameter_description:""};switch(s.jumpType){case 1:o.link_type="\u8DF3\u8F6C\u9875\u9762",o.link_parameter=s.jumpUrlPath,o.parameter_description=s.jumpAppName;break;case 2:o.link_type="\u56FE\u7247\u5F39\u7A97",o.link_parameter=s.jumpUrlPath;break;case 3:o.link_type="\u63D0\u4EA4\u6B65\u6570",o.link_parameter="\u63D0\u4EA4\u6B65\u6570";break;case 4:o.link_type="\u5206\u4EAB",o.link_parameter="\u5206\u4EAB";break;case 5:o.link_type="\u5B9A\u5236",o.link_parameter="\u7528\u9014";break;case 6:o.link_type="\u8BC4\u4EF7\u5F39\u7A97",o.link_parameter="\u8BC4\u4EF7\u5F39\u7A97";break}w("sensorTrack",{name:"TofinishClick",sensorData:o}),e[s.jumpType](s,a,t)},C0=(s,a,t)=>{s.jumpUrlType==1?window.location.href=s.jumpUrlPath:s.jumpUrlType==4?B.value?window.TopsportsApp.postMessage(JSON.stringify({method:"navigateToNative",data:s.jumpUrlPath})):v.value?window.location.href="/f/wap/#/appDownload?link="+s.jumpUrlPath:As(s.jumpUrlPath,`#miss-btn${a}`):s.jumpUrlType==2?(/\?/g.test(s.jumpUrlPath)?s.jumpUrlPath=`${s.jumpUrlPath}&taskId=${t.taskId}`:s.jumpUrlPath=`${s.jumpUrlPath}?taskId=${t.taskId}`,B.value?window.TopsportsApp.postMessage(JSON.stringify({method:"launchMiniProgram",data:{username:Y.value,path:`${s.jumpUrlPath}&timestamp=${new Date().getTime()}`}})):v.value&&(Q==="1"?as(s.jumpUrlPath):Q==="3"&&S.miniProgram.navigateTo({url:`/secondaryPage/pages/mid/index?appUrl=${encodeURIComponent(s.jumpUrlPath)}&appId=${s.jumpAppId}&type=1`}))):s.jumpUrlType==3?(/\?/g.test(s.jumpUrlPath)?s.jumpUrlPath=`${s.jumpUrlPath}&taskId=${t.taskId}`:s.jumpUrlPath=`${s.jumpUrlPath}?taskId=${t.taskId}`,B.value?window.TopsportsApp.postMessage(JSON.stringify({method:"launchMiniProgram",data:{username:l.value,path:`${s.jumpUrlPath}&timestamp=${new Date().getTime()}`}})):v.value&&(Q==="1"?S.miniProgram.navigateTo({url:`/secondaryPage/pages/mid/index?appUrl=${encodeURIComponent(s.jumpUrlPath)}&appId=${s.jumpAppId}&type=1`}):Q==="3"&&S.miniProgram.navigateTo({url:s.jumpUrlPath}))):s.jumpUrlType==5&&(/\?/g.test(s.jumpUrlPath)?s.jumpUrlPath=`${s.jumpUrlPath}&taskId=${t.taskId}`:s.jumpUrlPath=`${s.jumpUrlPath}?taskId=${t.taskId}`,B.value?window.TopsportsApp.postMessage(JSON.stringify({method:"launchMiniProgram",data:{username:s.jumpAppId,path:`${s.jumpUrlPath}&timestamp=${new Date().getTime()}`}})):v.value&&(Q==="1"?S.miniProgram.navigateTo({url:`/secondaryPage/pages/mid/index?appUrl=${encodeURIComponent(s.jumpUrlPath)}&appId=${s.jumpAppId}&type=1`}):Q==="3"&&S.miniProgram.navigateTo({url:s.jumpUrlPath})))},Y0=(s,a)=>{a0.value=s.jumpUrlPath,f.value=!0},U0=(s,a,t)=>{let e=Object.assign({},s,{jumpAppId:o0,jumpType:1,jumpUrlType:"2",jumpUrlPath:"/userContent/pages/authCenter/index?type=step"});C0(e,a,t)},j0=(s,a)=>{j.value=!0;let t={data:{brand:m,share_page:window.location.href,share_type:2},table:"e_share_page"};i0(t,2e3)},G0=async(s,a)=>{f0.value=Number(U.missionList[a].completeUpperLimit),P.value=k0(qs),await p0(),I.value=!0},X0=()=>v.value?1:B.value?2:O.value?3:4,T0=async s=>{let{data:a}=await is({data:{id:s.id,taskId:s.taskId},showLoading:!0});if(a.bizCode===2e4){U.rewardList=a.data.prizeList,C.value=k0(Fs),await p0(),u.value=!0,s0();let t=[];a.data.prizeList.map(o=>{o.prizeType===3?t.push(`${o.prizeValue}\u79EF\u5206`):t.push(o.prizeName)});let e={task_name:s.taskName,task_id:s.taskId,task_event_tab:s.taskEventCode,prize_num:a.data.prizeList.length,prize_name:t.join("+")};w("sensorTrack",{name:"GetprizeClick",sensorData:e})}},O0=async s=>{i0({data:{brand:m,evaluation_score:s},table:"e_evaluation_appstore"})},z0=async()=>{s0(),u.value=!1,setTimeout(()=>{C.value=""},500),H0()},S0=async s=>{I.value=!1,s||setTimeout(()=>{P.value=""},500)},N0=async()=>{s0(),f.value=!1,a0.value=""},J0=()=>{z.value?U.missionList.map((s,a)=>{a>x.taskFoldCount-1&&(s.visible=!1)}):(window.sensors.track("TaskMore",{}),U.missionList.map(s=>{s.visible=!0})),z.value=!z.value},V0=s=>{s.style.opacity=0,s.style.height=0},K0=(s,a)=>{m0.to(s,{opacity:1,height:"21.1vw",delay:(s.dataset.index-x.taskFoldCount)*.08,onComplete:a})},R0=(s,a)=>{m0.to(s,{opacity:0,height:0,delay:(U.missionList.length-1-s.dataset.index)*.08,onComplete:a})};c({getTask:$});let{missionList:u0,rewardList:r0}=_0(U);return(s,a)=>{const t=$0("wx-open-launch-weapp");return d(),p("div",Ws,[A("div",Zs,[_s,$s,A("div",{class:"head-refresh",onClick:a[0]||(a[0]=F(e=>$(!0),["stop"]))},[A("span",{class:K({iconfont:!0,"refresh-icon":!0,"refresh-icon-ani":n(W)})},"\uE658",2),se])]),A("div",ee,[E(ss,{tag:"div",css:!1,onBeforeEnter:V0,onEnter:K0,onLeave:R0},{default:D(()=>[(d(!0),p(A0,null,n0(n(u0),(e,o)=>M((d(),p("div",{class:"miss-item",key:e.taskId,"data-index":o},[A("img",{src:e.taskIcon,alt:"",class:"miss-icon"},null,8,ae),A("div",te,[A("div",oe,[A("div",{class:"miss-task-name",innerHTML:e.taskName},null,8,ie),e.ifShowCompleteGoalProgressBar?(d(),p("div",re,[ne,A("div",le,X(e.completeGoalProgressBar),1),G(' <div class="miss-step-goal">/{{item.goal}}</div> ')])):G("v-if",!0)]),A("div",{class:"miss-info-content",innerHTML:e.taskDesc},null,8,ge),A("div",Be,[e.ifShowCompleteUpperLimitProgressBar?(d(),p(A0,{key:0},[ce,de,A("span",fe,X(e.completeUpperLimitProgressBar)+"\u6B21",1),G(" <span>/{{item.upperLimit}}\u6B21</span> ")],64)):G("v-if",!0)])]),A("div",{class:K({"miss-btn":!0,"miss-btn-suc":e.taskStatus===2,"miss-btn-disabled":e.taskStatus===3}),id:`miss-btn${o}`,style:B0({backgroundColor:i.configData.taskBtnBgColor,color:i.configData.taskBtnColor}),onClick:r=>F0(e,o)},[D0(X(e.taskStatusName)+" ",1),n(O)&&!n(v)&&e.wechatJumpMiniUrl&&e.wechatJumpUserName&&e.taskStatus==2&&n(t0)?(d(),V(t,{key:0,onLaunch:r=>b0(e,o),"env-version":n(d0)?"release":"trial",path:e.wechatJumpMiniUrl,style:{position:"absolute",top:"0",left:"0",width:"100%",height:"100%",display:"block",color:"transparent"},username:e.wechatJumpUserName},{default:D(()=>[(d(),V(e0("script"),{type:"text/wxtag-template"},{default:D(()=>[Ce,(d(),V(e0("style"),null,{default:D(()=>[D0(" .test-inner{color:transparent;width:100vw;height:100vh;position:relative;font-size:20px;} ")]),_:1}))]),_:1}))]),_:2},1032,["onLaunch","env-version","path","username"])):G("v-if",!0)],14,xe)],8,Ae)),[[b,e.visible]])),128))]),_:1}),n(u0).length>i.taskFoldCount&&i.taskFoldCount>0?(d(),p("div",{key:0,class:"miss-toggle-btn",onClick:J0},[A("span",ue,X(n(z)?"\u6536\u8D77":"\u66F4\u591A\u4EFB\u52A1"),1),A("span",{class:K(["iconfont miss-toggle-icon",n(z)?"miss-toggle-icon-reverse":""])},"\uE7BE",2)])):G("v-if",!0)]),E(fs,{show:n(f),onCloseDialog:N0},{default:D(()=>[A("img",{src:n(a0),alt:"",class:"miss-img"},null,8,he)]),_:1},8,["show"]),(d(),V(e0(n(C)),{show:n(u),configData:i.configData,onCloseDialog:z0},{default:D(()=>[A("div",De,[(d(!0),p(A0,null,n0(n(r0),(e,o)=>(d(),p("div",{class:K({"miss-reward-item":n(r0).length<3,"miss-reward-item-mini":n(r0).length>=3}),key:o},[A("img",{src:e.prizeType===3?i.configData.popupPointsIcon:e.prizeImg,alt:"",class:"miss-reward-img"},null,8,ke),A("div",pe,X(e.prizeType===3?`\u83B7\u5F97${e.prizeValue}\u79EF\u5206`:e.prizeName),1)],2))),128))])]),_:1},40,["show","configData"])),(d(),V(e0(n(P)),{show:n(I),onCloseDialog:S0,onFeedConfirm:O0,feedCompleteUpperLimit:n(f0)},null,40,["show","feedCompleteUpperLimit"])),E(Gs,{show:n(j),onCloseDialog:a[1]||(a[1]=e=>es(j)?j.value=!1:j=!1)},null,8,["show"])])}}});var Pe=R(we,[["__scopeId","data-v-57a41cee"],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/components/mission/Mission.vue"]]);export{Pe as M};
