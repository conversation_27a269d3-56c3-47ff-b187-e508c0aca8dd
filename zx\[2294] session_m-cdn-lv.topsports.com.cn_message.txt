GET /m/assets/api_mission.16fe0a4f.js h2
host: m-cdn-lv.topsports.com.cn
origin: https://m.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: */*
sec-fetch-site: same-site
sec-fetch-mode: cors
sec-fetch-dest: script
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1



h2 200
server: Tengine
content-type: application/javascript; charset=utf-8
content-length: 465
date: Thu, 31 Jul 2025 12:13:19 GMT
x-trace-id: 123-9029-1753963999789-6399082
accept-ranges: bytes
via: cache27.l2cn8116[0,0,304-0,H], cache22.l2cn8116[0,0], cache22.l2cn8116[1,0], cache6.cn7330[0,0,200-0,H], cache10.cn7330[1,0]
last-modified: Thu, 31 Jul 2025 10:29:38 GMT
etag: "688b4592-1d1"
age: 220
ali-swift-global-savetime: 1753963999
x-cache: HIT TCP_MEM_HIT dirn:-2:-2
x-swift-savetime: Thu, 31 Jul 2025 12:16:27 GMT
x-swift-cachetime: 640
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f1e17539642195971729e

import{R as r}from"./index.963830f9.js";let e=new r,s="/";/m-test/.test(window.location.origin);const o=t=>e.get({url:s+"h5/taskcenter/getTaskList",showLoading:!1,...t}),n=t=>e.post({url:s+"h5/taskcenter/receiveReward",...t}),c=t=>e.post({url:s+"h5/taskcenter/dataReport",showLoading:!1,...t}),i=t=>e.get({url:s+"h5/taskcenter/getOneTaskStatus",showLoading:!1,...t}),p=t=>e.post({url:s+"h5/taskcenter/receiveTask",...t});export{n as a,i as b,c as d,o as g,p as r};
