GET /static/setCookieApplets.html?url=https%3A%2F%2Fm.topsports.com.cn%2Fm%2Fdailycenter%3FbrandCode%3DTS%26share%3Dtrue%26minienv%3D1&Authorization=12d685a2-61e1-402b-91ac-97f32abaf6ae&appletsSource=wx71a6af1f91734f18&memberId=8a7a099f80db7ca40180eed6f3203bc2&version=4.5.2 HTTP/1.1
Host: m.topsports.com.cn
Connection: keep-alive
Upgrade-Insecure-Requests: 1
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7
Sec-Fetch-Site: none
Sec-Fetch-Mode: navigate
Sec-Fetch-User: ?1
Sec-Fetch-Dest: document
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9
Cookie: acw_tc=3ccdc16a17539663780755909e73bd9273fa03bfaba895d5cd6ba0f4991dd3; Authorization=12d685a2-61e1-402b-91ac-97f32abaf6ae; appletsSource=wx71a6af1f91734f18; memberId=8a7a099f80db7ca40180eed6f3203bc2; version=4.5.2; sensorsdata2015jssdkcross=%7B%22%24device_id%22%3A%22198608b0b2c4fb-0489604a2a8fb28-673e1615-1440000-198608b0b2d67d%22%7D; sajssdk_2015_new_user_m_topsports_com_cn=1; sa_jssdk_2015_m_topsports_com_cn=%7B%22distinct_id%22%3A%228a7a099f80db7ca40180eed6f3203bc2%22%2C%22first_id%22%3A%228a7a099f80db7ca40180eed6f3203bc2%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk4NjA4YjBiMmM0ZmItMDQ4OTYwNGEyYThmYjI4LTY3M2UxNjE1LTE0NDAwMDAtMTk4NjA4YjBiMmQ2N2QiLCIkaWRlbnRpdHlfbG9naW5faWQiOiI4YTdhMDk5ZjgwZGI3Y2E0MDE4MGVlZDZmMzIwM2JjMiIsIiRpZGVudGl0eV9hbm9ueW1vdXNfaWQiOiI4YTdhMDk5ZjgwZGI3Y2E0MDE4MGVlZDZmMzIwM2JjMiJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%228a7a099f80db7ca40180eed6f3203bc2%22%7D%7D; QZ_SID=8191659A9B8535AAEBF0407A2D620919
If-None-Match: W/"688207b3-1607"
If-Modified-Since: Thu, 24 Jul 2025 10:15:15 GMT



HTTP/1.1 200 OK
Date: Thu, 31 Jul 2025 12:53:22 GMT
Content-Type: text/html; charset=utf-8
Transfer-Encoding: chunked
Connection: keep-alive
Server: Tengine
X-TRACE-ID: 123-95206-1753966402717-2839542
Last-Modified: Thu, 24 Jul 2025 04:10:58 GMT
ETag: W/"6881b252-1607"
Cache-Control: no-cache
Pragma: no-cache
Expires: 0
Content-Encoding: gzip

<!DOCTYPE html>
<!-- saved from url=(0014)about:internet -->
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>

<body style="zoom: 1">
    <!-- <script src="https://cdn.bootcss.com/vConsole/3.2.0/vconsole.min.js"></script> -->

    <script>
        var hostDict = ['topsports-ts51-uat.beats-digital.com','topsports-cny.beats-digital.com', 'hd.ysfaisco.cn', 'openauth-hd.fkw.com']
        var docCookies = {
            getItem: function (sKey) {
                return (
                    decodeURIComponent(
                        document.cookie.replace(
                            new RegExp(
                                "(?:(?:^|.*;)\\s*" +
                                encodeURIComponent(sKey).replace(/[-.+*]/g, "\\$&") +
                                "\\s*\\=\\s*([^;]*).*$)|^.*$",
                            ),
                            "$1",
                        ),
                    ) || null
                );
            },
            setItem: function (sKey, sValue, vEnd, sPath, sDomain, bSecure) {
                if (!sKey || /^(?:expires|max\-age|path|domain|secure)$/i.test(sKey)) {
                    return false;
                }
                var sExpires = "";
                if (vEnd) {
                    switch (vEnd.constructor) {
                        case Number:
                            sExpires =
                                vEnd === Infinity
                                    ? "; expires=Fri, 31 Dec 9999 23:59:59 GMT"
                                    : "; max-age=" + vEnd;
                            break;
                        case String:
                            sExpires = "; expires=" + vEnd;
                            break;
                        case Date:
                            sExpires = "; expires=" + vEnd.toUTCString();
                            break;
                    }
                }
                document.cookie =
                    encodeURIComponent(sKey) +
                    "=" +
                    encodeURIComponent(sValue) +
                    sExpires +
                    (sDomain ? "; domain=" + sDomain : "") +
                    (sPath ? "; path=" + sPath : "") +
                    (bSecure ? "; secure" : "");
                return true;
            },
            removeItem: function (sKey, sPath, sDomain) {
                if (!sKey || !this.hasItem(sKey)) {
                    return false;
                }
                document.cookie =
                    encodeURIComponent(sKey) +
                    "=; expires=Thu, 01 Jan 1970 00:00:00 GMT" +
                    (sDomain ? "; domain=" + sDomain : "") +
                    (sPath ? "; path=" + sPath : "");
                return true;
            },
            hasItem: function (sKey) {
                return new RegExp(
                    "(?:^|;\\s*)" +
                    encodeURIComponent(sKey).replace(/[-.+*]/g, "\\$&") +
                    "\\s*\\=",
                ).test(document.cookie);
            },
            keys: /* optional method: you can safely remove it! */ function () {
                var aKeys = document.cookie
                    .replace(/((?:^|\s*;)[^\=]+)(?=;|$)|^\s*|\s*(?:\=[^;]*)?(?:\1|$)/g, "")
                    .split(/\s*(?:\=[^;]*)?;\s*/);
                for (var nIdx = 0; nIdx < aKeys.length; nIdx++) {
                    aKeys[nIdx] = decodeURIComponent(aKeys[nIdx]);
                }
                return aKeys;
            },
        };
        // document.cookie = "appletsSource=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        // document.cookie = "Authorization=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        docCookies.removeItem('Authorization','/', '.topsports.com.cn');
        docCookies.removeItem('Authorization','/', '.topsports.com.cn');
        docCookies.removeItem('appletsSource');
        docCookies.removeItem('Authorization');
        docCookies.removeItem('appletsSource','/static',window.location.host);
        docCookies.removeItem('Authorization','/static',window.location.host);
        docCookies.removeItem('appletsSource','/static', '.topsports.com.cn');
        docCookies.removeItem('Authorization','/static', '.topsports.com.cn');
        var exp = new Date();
        exp.setTime(exp.getTime() + 60 * 60 * 24 * 1000);
        var url = "";
        location.search.split("?")[1].split("&").forEach(function (item, i) {
            var its = item.split("=");
            if (its[0] == "url") {
                url = decodeURIComponent(its[1]);
            } else {
                docCookies.setItem(its[0], its[1]?decodeURIComponent(its[1]):"", exp, '/', '.topsports.com.cn')
                // document.cookie = its[0] + "=" + (its[1] ? decodeURIComponent(its[1]) : "") + ";domain=.topsports.com.cn;path=/;expires=" + exp.toGMTString();
            }
        });
        var HOST = ''
        var arr = url.match(/https?:\/\/([^\/\?]+)/);
        if(arr.length>0){
            HOST = arr[0];
        }
        if(HOST.lastIndexOf('topsports.com.cn')>=0){
            if(HOST === window.location.origin){
                history.replaceState(null, '', url);
                window.location.reload();
            }else{
                window.location.href = url;
            }
        } else {
            let tag = false
            hostDict.forEach((item, index)=>{
                if (HOST.indexOf(item)){
                    tag = true
                }
            })
            if (tag) window.location.href = url;
        }
    </script>

</body>

</html>