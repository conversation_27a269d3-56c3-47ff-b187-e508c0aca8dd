GET /m/assets/use-route.69b0f6c5.js h2
host: m-cdn-lv.topsports.com.cn
origin: https://m.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: */*
sec-fetch-site: same-site
sec-fetch-mode: cors
sec-fetch-dest: script
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1



h2 200
server: Tengine
content-type: application/javascript; charset=utf-8
content-length: 285
date: Thu, 31 Jul 2025 12:13:19 GMT
x-trace-id: 123-95206-1753963999802-2478907
last-modified: Thu, 31 Jul 2025 10:29:38 GMT
etag: "688b4592-11d"
accept-ranges: bytes
via: cache45.l2cn8116[0,0,200-0,H], cache45.l2cn8116[0,0], cache45.l2cn8116[1,0], cache10.cn7330[0,0,200-0,H], cache10.cn7330[3,0]
age: 220
ali-swift-global-savetime: 1753963999
x-cache: HIT TCP_MEM_HIT dirn:-2:-2
x-swift-savetime: Thu, 31 Jul 2025 12:16:27 GMT
x-swift-cachetime: 640
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f1e17539642195551602e

import{$ as n}from"./index.963830f9.js";const c={to:[String,Object],url:String,replace:Boolean};function a({to:e,url:o,replace:t,$router:r}){e&&r?r[t?"replace":"push"](e):o&&(t?location.replace(o):location.href=o)}function i(){const e=n().proxy;return()=>a(e)}export{a,c as r,i as u};
