GET /m/assets/index.7400bf93.js h2
host: m-cdn-lv.topsports.com.cn
origin: https://m.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: */*
sec-fetch-site: same-site
sec-fetch-mode: cors
sec-fetch-dest: script
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
priority: u=1



h2 200
server: Tengine
content-type: application/javascript; charset=utf-8
content-length: 32178
date: Thu, 31 Jul 2025 12:02:25 GMT
x-trace-id: 123-7064-1753963345310-6349071
accept-ranges: bytes
via: cache26.l2cn8116[0,0,304-0,H], cache18.l2cn8116[1,0], cache18.l2cn8116[1,0], cache17.cn7330[0,0,200-0,H], cache10.cn7330[3,0]
last-modified: Thu, 31 Jul 2025 08:25:34 GMT
etag: "688b287e-7db2"
age: 874
ali-swift-global-savetime: 1753963345
x-cache: HIT TCP_MEM_HIT dirn:-2:-2
x-swift-savetime: Thu, 31 Jul 2025 12:16:27 GMT
x-swift-cachetime: 1385
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f1e17539642193811019e

import{_ as Z,d as ee,o,c as r,i as x,w as C,v as X,z as K,b as i,X as N,a1 as ge,a2 as Ke,g,e as Ze,h as et,l as z,Y as s,s as F,Z as q,a as c,a0 as j,p as be,m as Se,F as D,j as H,a3 as bt,L as tt,f as st,n as M,k as v,a5 as B,a7 as at,a6 as St,a8 as Ft,u as Et,r as Qe,H as $t,a4 as xt,aR as Tt,ab as Xe,aK as Bt,T as Q,t as Mt,$ as At,b4 as Lt,b5 as Pt}from"./index.963830f9.js";import{D as Ie}from"./index.dbd0cbcb.js";import{S as zt}from"./index.6ce7abf2.js";import{M as Nt}from"./Mission.ac64c021.js";import{A as Vt}from"./AdvertCenter.4b1f08c7.js";import{a as Rt,b as Ut,c as Yt,d as Ot,e as jt,s as Ht,f as qt,h as Jt}from"./api_checkin.43812b5a.js";import{h as Wt,g as Gt}from"./api_memberweek.ef769ed7.js";import{s as Qt}from"./saas_config.17584cb4.js";import"./use-route.69b0f6c5.js";import"./api_mission.16fe0a4f.js";import"./api.5870f003.js";import"./lottie.4c88a68e.js";import"./index.c6ddc126.js";import"./index.ca74db51.js";const Xt={class:"ck-dialog-container"},Kt=["onTouchmove"],Zt={class:"ck-dialog-content"},es=ee({__name:"CheckinDialog",props:{show:{type:Boolean,required:!0}},emits:["closeDialog"],setup(d,{emit:I}){const u=y=>{y.stopPropagation(),y.preventDefault()},f=()=>{I("closeDialog")};return(y,J)=>(o(),r("div",Xt,[x(ge,{name:"kmask",onClick:f,persisted:""},{default:C(()=>[X(i("div",{class:"ck-dialog-mask",onTouchmove:N(u,["stop"])},null,40,Kt),[[K,d.show]])]),_:1}),x(ge,{name:"kcontent",persisted:""},{default:C(()=>[X(i("div",Zt,[Ke(y.$slots,"default",{},void 0,!0)],512),[[K,d.show]])]),_:3})]))}});var ts=Z(es,[["__scopeId","data-v-38bc3555"],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/components/CheckinDialog.vue"]]);const ss={class:"ck-dialog-container"},as=["onTouchmove"],is={class:"ck-dialog-content"},ns=ee({__name:"CheckinRewardDialog",props:{show:{type:Boolean,required:!0}},emits:["closeDialog"],setup(d,{emit:I}){const u=y=>{y.stopPropagation(),y.preventDefault()},f=()=>{I("closeDialog")};return(y,J)=>(o(),r("div",ss,[x(ge,{name:"kmask",onClick:f,persisted:""},{default:C(()=>[X(i("div",{class:"ck-dialog-mask",onTouchmove:N(u,["stop"])},null,40,as),[[K,d.show]])]),_:1}),x(ge,{name:"kcontent",persisted:""},{default:C(()=>[X(i("div",is,[Ke(y.$slots,"default",{},void 0,!0)],512),[[K,d.show]])]),_:3})]))}});var os=Z(ns,[["__scopeId","data-v-14ec73b8"],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/components/CheckinRewardDialog.vue"]]);const Fe=d=>(be("data-v-6bb4e63a"),d=d(),Se(),d),ls={class:"cki-upgrade"},rs={class:"up-group"},us=Fe(()=>i("div",{class:"iconfont up-icon"},"\uE654",-1)),cs=Fe(()=>i("div",{class:"up-text"},"\u7B7E\u5230\u6709\u793C\u73A9\u6CD5\u5347\u7EA7\u4E2D\uFF0C\u656C\u8BF7\u671F\u5F85",-1)),ds=["onClick"],gs=Fe(()=>i("div",{class:"test-inner"},"1",-1)),ps=ee({__name:"upgrade",setup(d){let I=g(!1),u=g(!1),f=g("");Ze(()=>{I.value=window.isMiniprogramEnv,u.value=window.TopsportsAppEnv,f.value="gh_c9daa25f7c4f"});const y=()=>{I.value?j.miniProgram.reLaunch({url:"/pages/find/index"}):u.value&&window.TopsportsApp.postMessage(JSON.stringify({method:"navigateToNative",data:"/mall"}))};return(J,V)=>{const t=et("wx-open-launch-weapp");return o(),r("div",ls,[i("div",rs,[us,cs,i("div",{class:"up-btn",onClick:N(y,["stop"])},[z(" \u53BB\u5546\u57CE\u9996\u9875 "),!s(I)&&!s(u)?(o(),F(t,{key:0,path:"pages/index/index.html",style:{position:"absolute",top:"0",left:"0",width:"100%",height:"100%",display:"block",color:"transparent"},username:s(f)},{default:C(()=>[(o(),F(q("script"),{type:"text/wxtag-template"},{default:C(()=>[gs,(o(),F(q("style"),null,{default:C(()=>[z(" .test-inner{color:transparent;width:100vw;height:100vh;position:relative;font-size:20px;} ")]),_:1}))]),_:1}))]),_:1},8,["username"])):c("v-if",!0)],8,ds)])])}}});var vs=Z(ps,[["__scopeId","data-v-6bb4e63a"],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/pages/Interactive/dailyCenter/upgrade.vue"]]);const hs={},ks={class:"ck-skeleton"},_s=i("div",{class:"sk-box sk-top"},[i("div",{class:"sk-line-mini sk-left-mid"}),i("div",{class:"sk-line-mini sk-right-top"}),i("div",{class:"sk-line-mini sk-right-bottom"})],-1),fs={class:"sk-box sk-mid"},ms=i("div",{class:"sk-core-top"},[i("div",{class:"sk-line-mini sk-left-top"}),i("div",{class:"sk-line sk-left-bottom"}),i("div",{class:"sk-line-mini sk-right-mid"})],-1),ys={class:"sk-core-bottom"},ws={class:"sk-core-group"},Ds={class:"sk-core-fat"},Cs=i("div",{class:"sk-core-large"},null,-1),Is=bt('<div class="sk-box sk-bottom"><div class="sk-mis-line"><div class="sk-line-mini sk-left-top"></div><div class="sk-line sk-left-bottom"></div></div><div class="sk-mis-line"><div class="sk-line-mini sk-left-top"></div><div class="sk-line sk-left-bottom"></div><div class="sk-line-mini sk-right-mid"></div></div><div class="sk-mis-line"><div class="sk-line-mini sk-left-top"></div><div class="sk-line sk-left-bottom"></div><div class="sk-line-mini sk-right-mid"></div></div><div class="sk-mis-line"><div class="sk-line-mini sk-left-top"></div><div class="sk-line sk-left-bottom"></div><div class="sk-line-mini sk-right-mid"></div></div><div class="sk-mis-line"><div class="sk-line-mini sk-left-top"></div><div class="sk-line sk-left-bottom"></div><div class="sk-line-mini sk-right-mid"></div></div><div class="sk-mis-line"><div class="sk-line-mini sk-left-top"></div><div class="sk-line sk-left-bottom"></div><div class="sk-line-mini sk-right-mid"></div></div><div class="sk-mis-line"><div class="sk-line-mini sk-left-top"></div><div class="sk-line sk-left-bottom"></div><div class="sk-line-mini sk-right-mid"></div></div></div>',1);function bs(d,I){return o(),r("div",ks,[_s,i("div",fs,[ms,i("div",ys,[i("div",ws,[(o(),r(D,null,H(7,(u,f)=>i("div",Ds)),64))]),Cs])]),Is])}var Ss=Z(hs,[["render",bs],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/pages/Interactive/dailyCenter/skeleton.vue"]]);const Fs=d=>(be("data-v-784c50f9"),d=d(),Se(),d),Es=Fs(()=>i("li",null,"0",-1)),$s={width:"0",height:"0"},xs={id:"blur"},Ts=["stdDeviation"],Bs=ee({__name:"ScrollNum",props:{as:{type:String,required:!0},i:{type:Number,required:!0},delay:{type:Number,required:!0},blur:{type:Number,required:!0}},setup(d){const I=d;g(null);let u=g(!0);return tt(()=>I.i,(f,y)=>{u.value=!0},{deep:!0,immediate:!0}),st(()=>{}),(f,y)=>(o(),F(q(d.as),{class:M(["scroll-num",{"border-animate":s(u)}]),style:B({"--i":d.i,"--delay":d.delay}),onAnimationend:y[0]||(y[0]=J=>at(u)?u.value=!1:u=!1)},{default:C(()=>[i("ul",{ref:"ul",class:M({animate:s(u)})},[(o(),r(D,null,H(10,(J,V)=>i("li",null,v(V),1)),64)),Es],2),(o(),r("svg",$s,[i("filter",xs,[i("feGaussianBlur",{in:"SourceGraphic",stdDeviation:`0 ${d.blur}`},null,8,Ts)])]))]),_:1},40,["class","style"]))}});var Ms=Z(Bs,[["__scopeId","data-v-784c50f9"],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/components/ScrollNum.vue"]]);const E=d=>(be("data-v-2e655920"),d=d(),Se(),d),As={key:0},Ls=["src"],Ps=E(()=>i("div",{class:"test-inner"},"1",-1)),zs=["onClick"],Ns=["src"],Vs=E(()=>i("span",{class:"iconfont cki-coin-more"},"\uE871",-1)),Rs=E(()=>i("span",null,"\u6D3B\u52A8\u89C4\u5219",-1)),Us=E(()=>i("span",{class:"iconfont rules-more"},"\uE871",-1)),Ys=[Rs,Us],Os={class:"cki-main"},js={class:"cki-content"},Hs=["onClick"],qs=["src"],Js={class:"cki-header"},Ws={class:"cki-header-continue"},Gs=E(()=>i("span",{class:"cki-header-text"},"\u5DF2\u8FDE\u7EED\u7B7E\u5230",-1)),Qs={class:"cki-header-num"},Xs=E(()=>i("span",{class:"cki-header-text"},"\u5929",-1)),Ks={key:0,class:"cki-header-leftday"},Zs={class:"cki-notice-group"},ea=E(()=>i("div",{class:"cki-notice-text"},"\u5F00\u542F\u63D0\u9192",-1)),ta={class:"cki-notice-toggle"},sa=["onClick"],aa={class:"cki-body"},ia={class:"cki-ymview"},na={class:"ymview-content"},oa={class:"cki-week"},la={class:"cki-week-item"},ra=["onClick","data-index"],ua={key:0,class:"cki-day-pic"},ca=["src"],da=["src"],ga=["src"],pa={key:0,class:"cki-day-picpoint"},va=["src"],ha=["src"],ka={key:1,class:"cki-day-text"},_a={key:3,class:"cki-hoilday-text"},fa=E(()=>i("span",{class:"cki-cover-text"},"\u5C55\u5F00",-1)),ma=E(()=>i("span",{class:"iconfont cki-cover-icon"},"\uE7C0",-1)),ya=[fa,ma],wa={class:"cki-footer"},Da=["src"],Ca=E(()=>i("div",{class:"test-inner"},"1",-1)),Ia=["innerHTML"],ba={class:"rules-bg"},Sa={class:"ck-dialog-group"},Fa=["src"],Ea=E(()=>i("div",{class:"ck-dialog-title"},"\u7B7E\u5230\u6210\u529F",-1)),$a={class:"ck-dialog-tips"},xa={key:0,class:"ck-dialog-tips2"},Ta={key:1,class:"ck-dialog-reward-single"},Ba=["src"],Ma={class:"ck-dialog-reward-text"},Aa=["src"],La={class:"ck-dialog-reward-text"},Pa={class:"ck-dialog-reward-sub-text"},za=["src"],Na=["src"],Va=E(()=>i("div",{class:"btn-noticed"}," \u6211\u77E5\u9053\u4E86 ",-1)),Ra=[Va],Ua={class:"reward-dialog"},Ya=["src"],Oa=ee({name:"signave"}),ja=ee({...Oa,setup(d){const I=St(window.location.href),u=At(),f=u==null?void 0:u.appContext.config.globalProperties.brandCode,y=Ft(),J=Et();let V=!1,t=Qe({weekList:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],date:new Date,current:new Date,fullCalendar:!0,dayList:[],rulesVisible:!1,checkinDialogVisible:!1,rewardDialogVisible:!1,actInfo:{taskFoldCount:0},consecutiveDays:0,continueDays:0,reminderStatus:!1,todaySignInStatus:0,rewardList:[],rewardTips:"",rewardSubTips:"",dailySignIntegral:0}),pe=g(0),ve=g(0),b=g(!1),S=g(!1),te=g(!1),R=g(!1),A=g(!1),he=g(""),ne=g(""),U=g(0),Ee=g(""),$e=g(""),ke=g(""),_e=g(""),it=g(""),xe=g(!1),nt=g(`${window.location.origin}/m/dailycenter?brandCode=${f}&share=true&distributionH5=true`),fe=g(0),W=g({linkAppId:"",linkType:0,linkUrl:""}),Te=!0;const Be=g(null);let Me="",Ae="",Y="",Le=!1,ot=$t(()=>{let e=String(pe.value),a=[];for(const l of e)a.push(parseInt(l));return a}),oe=[],Pe=g([""]),G={act_track:(u==null?void 0:u.appContext.config.globalProperties.act_track)||window.sessionStorage.getItem("act_track")||"",distributor_no:(u==null?void 0:u.appContext.config.globalProperties.distributorNo)||window.sessionStorage.getItem("distributorNo")||"",employee_id:(u==null?void 0:u.appContext.config.globalProperties.employeeCode)||window.sessionStorage.getItem("employeeCode")||"",distributor_shop_no:(u==null?void 0:u.appContext.config.globalProperties.storeNo)||window.sessionStorage.getItem("storeNo")||"",vip_level:"",supervip_level:!1},ze="",le="",m="",Ne=0,k=Qe({});Pe.value=Qt.dailyCenter.advert[f].adPositionCodes,tt(()=>t.dayList,(e,a)=>{ve.value=Math.ceil(e.length/7)},{deep:!0,immediate:!0}),Ze(()=>{b.value=window.isMiniprogramEnv,S.value=window.TopsportsAppEnv,te.value=window.isWechat,ne.value="gh_c9daa25f7c4f",xt(f,!0),b.value||S.value&&(window.detectNotiCallback=e=>{Le=!0,e?we(t.reminderStatus):Ie.alert({title:"\u5F00\u542F\u63A8\u9001\u901A\u77E5",message:"\u5373\u523B\u5F00\u542F\u901A\u77E5\uFF0C\u7CFB\u7EDF\u5C06\u4E8E\u6BCF\u65E512\u70B9\u63A8\u9001\u7B7E\u5230\u670D\u52A1\u63D0\u9192\u54E6\uFF5E",confirmButtonText:"\u7ACB\u5373\u5F00\u542F",confirmButtonColor:"#222222"}).then(()=>{window.TopsportsApp.postMessage(JSON.stringify({method:"openAppSettings"}))})})}),st(()=>{le="\u76F4\u63A5\u6253\u5F00",S.value?le=window.localStorage.getItem("webviewPreviousPage")||"\u76F4\u63A5\u6253\u5F00":le=I.previousPage||y.params.previousPage||"\u76F4\u63A5\u6253\u5F00",Y=I.activityId||"",dt(),rt()}),Tt(()=>{console.log("\u6267\u884C\u4E86onActivated"),console.log("route---",y),Te?Te=!1:(Ve(),De())});const lt=e=>{let a={...e.sensorData,...G};window.sensors.track(e.name,a)},Ve=()=>{let e={previous_page:le,...G};window.sensors.track("SignPageView",e)},rt=async()=>{let{data:e}=await Wt({showLoading:!1});if(e.bizCode===2e4){let a=e.data;a.loginStatus&&(ze=a.baseLevelName||"",Ne=a.levelCode||0,G.vip_level=ze||"",G.supervip_level=Ne===1001)}Ve()},ut=async e=>{let{data:a}=await qt({params:{actId:e},showLoading:!1});if(a.bizCode===2e4){let l=a.data;l&&Object.assign(k,l)}},ct=async()=>{m=`${window.location.origin}${window.location.pathname}?brandCode=${f}&share=true&distributionH5=true`,Y&&(m=`${m}&activityId=${Y}`);try{let{data:e}=await Gt({params:{},showLoading:!1});e.bizCode===2e4&&e.data&&(ke=e.data.employeeNo,_e=e.data.shopNo,it.value=e.data.distributorNo),ke&&(m=`${m}&employeeCode=${ke}`),e.data.distributorNo&&(m=`${m}&distributorNo=${e.data.distributorNo}`),_e?m=`${m}&storeNo=${_e}&utm_source=distributor&utm_medium=post`:m=`${m}&utm_source=nuser&utm_medium=post`,b.value?m=`${m}&act_track=38`:S.value?m=`${m}&act_track=18`:te.value&&(m=`${m}&act_track=28`),De()}catch{De()}},dt=async()=>{let{data:e}=await Rt({params:{brandCode:f,activityId:Y||null},showLoading:!1});if(e.bizCode===2e4){let a=e.data;if(t.actInfo=a,t.actInfo)if(t.actInfo.signInSwitch===1){if(ut(a.activityId),ct(),Promise.all([Ut({showLoading:!1}),Yt({showLoading:!1})]).then(l=>{l[0].data.bizCode===2e4?l[1].data.bizCode===2e4?(t.date=new Date(l[0].data.data.timestamp),t.current=new Date(l[0].data.data.timestamp),me(),pt(l[0].data.data.timestamp),ae(),Re(),t.actInfo.showTaskList===1&&re(),R.value=!0):l[1].data.bizCode===99999&&(t.date=new Date(l[0].data.data.timestamp),t.current=new Date(l[0].data.data.timestamp),b.value?j.miniProgram.redirectTo({url:"/pages/auth/index?url="+encodeURIComponent(window.location.href)}):S.value?window.TopsportsApp.postMessage(JSON.stringify({method:"openLogin",data:-1})):(xe.value=!0,pe.value=99999,ae(),se(),t.actInfo.showTaskList===1&&re(),Xe(()=>{R.value=!0,A.value=!0}))):console.error("\u83B7\u53D6\u65F6\u95F4\u9519\u8BEF\uFF0C\u83B7\u53D6\u767B\u5F55\u6001\u9519\u8BEF")}).catch(l=>{console.error(l),console.error("\u83B7\u53D6\u65F6\u95F4\u9519\u8BEF:error\uFF0C\u83B7\u53D6\u767B\u5F55\u6001\u9519\u8BEF:error")}),!b.value&&!S.value&&te.value&&(W.value=Object.assign({},t.actInfo.backImgH5WxLinkDTO),W.value&&W.value.linkType===2)){W.value.linkUrl=W.value.linkUrl.replace("?",".html?");let l=new Image;l.onload=n=>{console.log(l.width,l.height),fe.value=l.height/l.width*100},l.src=t.actInfo.backgroundImg}}else A.value=!0,R.value=!1;else R.value=!1}else R.value=!1},me=async()=>{let{data:e}=await Ot({});e.bizCode===2e4&&(pe.value=e.data.validAmount)};Bt("getPoints",me);const Re=async()=>{let e={params:{startTime:Me,endTime:Ae,brandCode:f},showLoading:!1};Y&&(e.params.activityId=Y);try{let{data:a}=await jt(e);if(a.bizCode===2e4){A.value=!0;let l=a.data;t.consecutiveDays=l.consecutiveDays,t.continueDays=l.continueDays,t.reminderStatus=l.reminderStatus===1,t.todaySignInStatus=l.todaySignInStatus,t.dailySignIntegral=l.dailySignIntegral,oe=l.signInCalendarDateList,se()}else A.value=!1,Q(a.bizMsg)}catch(a){A.value=!1,console.error(a),console.log("\u83B7\u53D6\u65E5\u5386\u62A5\u9519")}},gt=async()=>{if(!(t.todaySignInStatus===1||V===!0))try{V=!0;let{data:e}=await Jt({data:{activityId:t.actInfo.activityId,brandCode:f}});if(V=!1,e.bizCode===2e4){let a=e.data.signInTips.split("-"),l=!1;t.rewardTips=a[0],a.length===2&&(t.rewardSubTips=a[1]);let n=e.data.signInPrizeSendResultDTOList,h;if((p=>{p[p.\u65E5\u7B7E=1]="\u65E5\u7B7E",p[p.\u8282\u65E5\u7B7E\u5230=2]="\u8282\u65E5\u7B7E\u5230",p[p.\u8FDE\u7B7E=4]="\u8FDE\u7B7E"})(h||(h={})),n.filter(p=>p.prizeType!==3).length===0){let p=0,_=[];n.map(w=>{p+=parseInt(w.prizeValue),_.push(`${h[w.prizeSource]}${w.prizeValue}\u79EF\u5206`)}),Ee.value=`\u83B7\u5F97${p}\u79EF\u5206`,$e.value=_.join("+")}else{let p=n.length;n.map(_=>{_.prizeSource===2&&(l=!0),_.rewardText=`${p>=3?h[_.prizeSource]:""}${_.prizeType===3?_.prizeValue:""}${_.prizeName}`})}t.rewardList=n,t.checkinDialogVisible=!0,t.todaySignInStatus=1,Re(),setTimeout(()=>{Be.value.getTask()},2e3),window.sensors.track("SignButtonClick",{is_success:!0,continue_sign_days:t.consecutiveDays+1,is_festival_sign:l,...G})}else window.sensors.track("SignButtonClick",{is_success:!1,fail_reason:e.bizMsg,continue_sign_days:t.consecutiveDays,...G}),Q(e.bizMsg)}catch(e){V=!1,console.error(e),console.log("\u7B7E\u5230:error",e)}},ye=e=>{let a=Ye(e);e.setDate(1);let l=Ue(e);e.setDate(a);let n=Ue(e),h=[],p=l===0?0:l,_=7-(n+1);if(p>0){e.setDate(1);let w=new Date(e);w.setMonth(e.getMonth()-1);let L=Ye(w);for(;p>0;){let P=w.getFullYear(),O=w.getMonth()+1,ie=L-(p-1),Ce=`${P}-${O}-${ie}`;h.push({year:P,month:O,day:ie,date:Ce}),p--}}for(let w=1;w<=a;w++){let L=e.getFullYear(),P=e.getMonth()+1,O=`${L}-${P}-${w}`;h.push({year:L,month:P,day:w,date:O})}if(_>0){let w=new Date(e);w.setDate(1),w.setMonth(w.getMonth()+1);let L=[];for(;_>0;)L.push(_),_--;L.reverse(),L.map(P=>{let O=w.getFullYear(),ie=w.getMonth()+1,Ce=`${O}-${ie}-${P}`;h.push({year:O,month:ie,day:P,date:Ce})})}return h},se=()=>{for(let e=0;e<t.dayList.length;e++){t.dayList[e].signIntegral=t.dailySignIntegral,ht(t.dayList[e])?t.dayList[e].previewDay=!0:t.dayList[e].previewDay=!1;for(let a=0;a<oe.length;a++)`${t.dayList[e].year}-${t.dayList[e].month<10?"0"+t.dayList[e].month:t.dayList[e].month}-${t.dayList[e].day<10?"0"+t.dayList[e].day:t.dayList[e].day}`===oe[a].date&&(t.dayList[e]=Object.assign({},t.dayList[e],oe[a]))}},ae=()=>{t.dayList=ye(t.date)},Ue=e=>e.getDay(),Ye=e=>{let a=new Date(e);return a.setDate(15),a.setMonth(a.getMonth()+1),a.setDate(0),a.getDate()},Oe=e=>{if(e==="preview")if(U.value>-3){let a=t.date.getMonth()-1;a===-1&&(a=11,t.date.setFullYear(t.date.getFullYear()-1)),t.date.setDate(1),t.date.setMonth(a),ae(),se(),U.value-=1}else return Q("\u5F80\u524D\u6700\u591A\u53EF\u67E5\u770B3\u4E2A\u6708"),!1;else if(U.value<1){let a=t.date.getMonth()+1;a===12&&(a=0,t.date.setFullYear(t.date.getFullYear()+1)),t.date.setDate(1),t.date.setMonth(a),ae(),se(),U.value+=1}else return Q("\u5F80\u540E\u6700\u591A\u53EF\u67E5\u770B1\u4E2A\u6708"),!1},pt=e=>{let a=new Date(e);const l=a.getMonth();l-3<0?(a.setDate(1),a.setFullYear(a.getFullYear()-1),a.setMonth(12+(l-3))):(a.setDate(1),a.setMonth(l-3));let n=`${a.getFullYear()}/${a.getMonth()+1}/${a.getDate()} 00:00:00`,h=ye(new Date(n)),p=new Date(e);p.setDate(1),p.setMonth(p.getMonth()+1);let _=ye(p);Me=`${h[0].year}-${h[0].month}-${h[0].day} 00:00:00`,Ae=`${_[_.length-1].year}-${_[_.length-1].month}-${_[_.length-1].day} 00:00:00`},vt=e=>{let a="";e.holidaySignInDay!==null&&(he.value=e.holidaySignInDay.prizeImg,t.rewardDialogVisible=!0,a="FestivalSignGiftClick"),e.continueSignInDay!==null&&e.holidaySignInDay===null&&(he.value=e.continueSignInDay.prizeImg,t.rewardDialogVisible=!0,a="ContinueSignGiftClick"),window.sensors.track(a,{pic_description:a==="FestivalSignGiftClick"?e.holidaySignInDay.prizeDesc:e.continueSignInDay.prizeDesc,pic_url:a==="FestivalSignGiftClick"?e.holidaySignInDay.prizeImg:e.continueSignInDay.prizeImg})},re=async()=>{t.fullCalendar&&je(),await Xe(),t.fullCalendar=!t.fullCalendar},je=()=>{let e=-1;if(t.dayList.forEach((a,l)=>{He(a)&&(e=l)}),e>=0){let a=e-e%7,l=a+6;for(a;a<=l;a++)t.dayList[a].reduceShow=!0;U.value=0}else e=null,t.date=new Date(t.current),ae(),je(),se()},He=e=>t.current.getFullYear()===e.year&&t.current.getMonth()+1===e.month&&t.current.getDate()===e.day,ht=e=>{let a=`${e.year}/${e.month<10?"0"+e.month:e.month}/${e.day<10?"0"+e.day:e.day} 00:00:00`,l=t.current.getMonth()+1,n=`${t.current.getFullYear()}/${l&&"0"+l}/${t.current.getDate()<10?"0"+t.current.getDate():t.current.getDate()} 00:00:00`;return new Date(a).getTime()<new Date(n).getTime()},qe=()=>{t.reminderStatus?Ie.confirm({message:"\u5173\u95ED\u63D0\u9192\u540E\uFF0C\u53EF\u80FD\u4F1A\u5FD8\u8BB0\u9886\u53D6\u79EF\u5206\u54E6\uFF5E\u786E\u8BA4\u5173\u95ED\u4E48\uFF1F",confirmButtonText:"\u786E\u8BA4\u5173\u95ED",confirmButtonColor:"#92929D",cancelButtonText:"\u6211\u518D\u60F3\u60F3",cancelButtonColor:"#262626",showCancelButton:!0}).then(()=>{we(t.reminderStatus)}):b.value?Ie.alert({title:"\u6E29\u99A8\u63D0\u793A",message:"\u5F00\u542F\u63D0\u9192\u540E\uFF0C12:00\u524D\u672A\u5B8C\u6210\u7B7E\u5230\u6211\u4EEC\u5C06\u7ED9\u60A8\u53D1\u9001\u63D0\u9192\u3002\u8BF7\u786E\u4FDD\u60A8\u5DF2\u5173\u6CE8\u201CTOPSPORTS\u516C\u4F17\u53F7\u201D\uFF0C\u5426\u5219\u65E0\u6CD5\u6210\u529F\u63A5\u6536\u63D0\u9192\u54E6\uFF5E",confirmButtonText:"\u6211\u77E5\u9053\u4E86",confirmButtonColor:"#92929D"}).then(()=>{we(t.reminderStatus)}):S.value&&(window.TopsportsApp.postMessage(JSON.stringify({method:"detectNoti"})),setTimeout(()=>{Le===!1&&Q("\u64CD\u4F5C\u5931\u8D25\uFF0C\u8BF7\u66F4\u65B0\u6ED4\u640F\u8FD0\u52A8APP\u81F3\u6700\u65B0\u7248\u672C")},1e3))},we=async e=>{console.log("value",e);let{data:a}=await Ht({data:{activityId:t.actInfo.activityId,status:e?0:1}});a.bizCode===2e4&&(t.reminderStatus=!e,Q(t.reminderStatus?"\u5DF2\u5F00\u542F\u6210\u529F":"\u5DF2\u5173\u95ED\u63D0\u9192"),window.sensors.track(t.reminderStatus?"TurnOnSignRemind":"TurnOffSignRemind",{}))},De=()=>{j.ready(()=>{b.value&&j.miniProgram.postMessage({data:{url:m,shareTitle:t.actInfo.shareTitle,shareImage:t.actInfo.shareImg,pageType:115}}),j.onMenuShareAppMessage({userName:ne.value,title:t.actInfo.shareTitle,desc:t.actInfo.shareDesc,link:m,imgUrl:t.actInfo.shareImg,success:window.shareMessageConfigCallback}),j.onMenuShareTimeline({title:t.actInfo.shareTitle,link:m,imgUrl:t.actInfo.shareImg,success:window.shareTimelineConfigCallback})})};window.shareMessageConfigCallback=window.shareMessageCallback=()=>{},window.shareTimelineConfigCallback=window.shareTimelineCallback=()=>{};const kt=()=>{let e=t.actInfo.linkType;if(b.value){let a=`/pages/webview/index?url=${encodeURIComponent(window.location.origin+"/m/authpoints?brandCode="+f)}`,l="/userContent/pages/myIntergal/index";j.miniProgram.navigateTo({url:e===1?a:l})}else if(S.value){let a=`/webview?url=${window.location.origin}/m/authpoints?brandCode=${f}`,l="/points_mall/history";window.TopsportsApp.postMessage(JSON.stringify({method:"navigateToNative",data:e===1?a:l}))}},Je=()=>{t.checkinDialogVisible=!1,me()},_t=()=>{t.reminderStatus?t.rewardList.length>=2&&We():qe(),Je()},We=()=>{J.push({name:"rewardcenter",query:{brandCode:f,activityId:t.actInfo.activityId}})},ft=()=>{let e=null;if(S.value?e=t.actInfo.backImgAppLinkDTO:b.value?e=t.actInfo.backImgXcxLinkDTO:te.value?e=t.actInfo.backImgH5WxLinkDTO:e=t.actInfo.backImgH5BrowserLinkDTO,(e==null?void 0:e.linkType)===1)window.location.href=e.linkUrl;else if(S.value)window.TopsportsApp.postMessage(JSON.stringify({method:"navigateToNative",data:e==null?void 0:e.linkUrl}));else if(b.value)Lt(e==null?void 0:e.linkUrl);else if(te.value)return!1},mt=()=>y.name==="signavenue"?"calc(6rem + env(safe-area-inset-bottom))":"0px";let{weekList:yt,date:ue,reminderStatus:ce,current:Ha,fullCalendar:$,dayList:wt,checkinDialogVisible:Dt,todaySignInStatus:de,rewardDialogVisible:Ct,rewardList:T,rewardTips:It,rewardSubTips:Ge}=Mt(t);return(e,a)=>{const l=et("wx-open-launch-weapp");return o(),r("div",{class:"cki-container",style:B({"padding-bottom":mt()})},[s(R)&&s(A)?(o(),r("div",As,[i("img",{class:"cki-banner",src:s(t).actInfo&&s(t).actInfo.backgroundImg?s(t).actInfo.backgroundImg:"https://m-cdn-lv.topsports.com.cn/fe/mp/member_images/checkin-banner-1.png",onClick:ft},null,8,Ls),!s(S)&&!s(b)&&s(fe)>0?(o(),r("div",{key:0,class:"checkin-cover",style:B({height:`${s(fe)}vw`})},[x(l,{path:`${s(W).linkUrl}`,style:{position:"absolute",top:"0",left:"0",width:"100%",height:"100%",display:"block",color:"transparent"},username:s(ne)},{default:C(()=>[(o(),F(q("script"),{type:"text/wxtag-template"},{default:C(()=>[Ps,(o(),F(q("style"),null,{default:C(()=>[z(" .test-inner{color:transparent;width:100vw;height:100vh;position:relative;font-size:20px;} ")]),_:1}))]),_:1}))]),_:1},8,["path","username"])],4)):c("v-if",!0),i("div",{class:"cki-points",onClick:N(kt,["stop"])},[i("img",{class:"cki-coin",src:s(k).pointsIcon,alt:""},null,8,Ns),i("ul",null,[(o(!0),r(D,null,H(s(ot),(n,h)=>(o(),F(Ms,{key:h,as:"li",i:n,delay:h+1,blur:2},null,8,["i","delay"]))),128))]),Vs],8,zs),i("div",{class:"cki-rules-entry",onClick:a[0]||(a[0]=n=>s(t).rulesVisible=!s(t).rulesVisible)},Ys),i("div",Os,[i("div",js,[i("div",{class:"cki-rewards",onClick:N(We,["stop"]),style:B({background:s(k).myPrizeBtnBg})},[c(' <span class="iconfont cki-rewards-icon">&#xe652;</span> '),i("img",{src:s(k).myPrizeIcon,mode:"widthFix",alt:"",class:"cki-rewards-icon"},null,8,qs),i("span",{class:"cki-rewards-text",style:B({color:s(k).myPrizeTextColor})},"\u6211\u7684\u5956\u54C1",4)],12,Hs),i("div",Js,[i("div",Ws,[Gs,i("span",Qs,v(s(t).consecutiveDays),1),Xs]),s(t).continueDays?(o(),r("div",Ks,[i("span",null,"\u518D\u8FDE\u7B7E"+v(s(t).continueDays)+"\u5929\u53EF\u9886\u989D\u5916\u5956\u52B1",1),i("span",{class:"iconfont cki-header-ques-icon",onClick:a[1]||(a[1]=n=>s(t).rulesVisible=!s(t).rulesVisible)},"\uE653")])):c("v-if",!0),i("div",Zs,[ea,i("div",ta,[x(s(zt),{modelValue:s(ce),"onUpdate:modelValue":a[2]||(a[2]=n=>at(ce)?ce.value=n:ce=n),size:19,"inactive-color":"#E5E5E5","active-color":s(k).reminderOnColor||"#FF2F69"},null,8,["modelValue","active-color"])]),i("div",{class:"cki-notice-cover",onClick:N(qe,["stop"])},null,8,sa)])]),i("div",aa,[i("div",{class:"cki-top-view",style:B({height:s($)?"22vw":"0vw"})},[i("div",ia,[i("span",{class:M(["iconfont ymview-preview-icon",s(U)<=-3?"ymview-icon-disabled":""]),onClick:a[3]||(a[3]=n=>Oe("preview"))},"\uE68C",2),i("div",na,v(s(ue).getFullYear())+"\u5E74"+v(s(ue).getMonth()<9?`0${s(ue).getMonth()+1}`:s(ue).getMonth()+1)+"\u6708",1),i("span",{class:M(["iconfont ymview-next-icon",s(U)>=1?"ymview-icon-disabled":""]),onClick:a[4]||(a[4]=n=>Oe("next"))},"\uE68C",2)]),i("div",oa,[(o(!0),r(D,null,H(s(yt),(n,h)=>(o(),r("div",la,v(n),1))),256))])],4),i("div",{class:M({"cki-day":!0,"cki-day-inline":!s($)}),style:B({height:s($)?`${s(ve)*19.66}vw`:"17vw"})},[(o(!0),r(D,null,H(s(wt),(n,h)=>(o(),r("div",{key:n.date,class:M({"cki-day-item":!0,"cki-day-item-opacity":n.previewDay,"cki-day-item-checked":n.signInStatus===1,"cki-reduce-hidden":!s($)&&!n.reduceShow}),onClick:p=>vt(n),"data-index":h},[s($)||!s($)&&n.reduceShow?(o(),r("div",{key:0,class:"cki-day-group",style:B({background:n.signInStatus===1?s(k).dateSignedBgColor:s(k).dateUnsignedBgColor})},[n.date?(o(),r("div",ua,[c("\u6B64\u5904\u7528item.date\u505A\u5224\u65AD\u662F\u4E3A\u4E86\u9632\u6B62\u5207\u6362\u6708\u4EFD\u7684\u65F6\u5019\u6709\u4E2A\u521D\u59CB\u56FE\u7247\u95EA\u70C1"),n.continueSignInDay==null&&n.holidaySignInDay==null?(o(),r(D,{key:0},[s(xe)?(o(),r("img",{key:0,src:s(k).dateExpiredIcon,alt:"",class:"cki-day-img"},null,8,ca)):(o(),r(D,{key:1},[n.previewDay?(o(),r("img",{key:0,src:n.signInStatus===1?s(k).historyDoneIcon:s(k).dateExpiredIcon,alt:"",class:"cki-day-img"},null,8,da)):(o(),r(D,{key:1},[i("img",{src:n.signInStatus===1?s(k).todayDoneIcon:s(k).dailySignIcon,alt:"",class:"cki-day-img"},null,8,ga),n.signInStatus!==1?(o(),r("div",pa,"+"+v(n.signIntegral),1)):c("v-if",!0)],64))],64))],64)):c("v-if",!0),c(' 992023\u5C4F\u853D <img v-if="item.continueSignInDay!=null&&item.holidaySignInDay==null" src="https://m-cdn-lv.topsports.com.cn/fe/mp/member_images/icon-checkin-redpack.png" alt="" class="cki-day-conimg"> '),n.continueSignInDay!=null&&n.holidaySignInDay==null?(o(),r("img",{key:1,src:s(k).contSignIcon,alt:"",class:"cki-day-conimg"},null,8,va)):c("v-if",!0),n.holidaySignInDay!=null?(o(),r("img",{key:2,src:n.holidaySignInDay.icon,alt:"",class:"cki-day-img"},null,8,ha)):c("v-if",!0)])):c("v-if",!0),He(n)?(o(),r("div",ka,"\u4ECA\u65E5")):(o(),r("div",{key:2,class:M({"cki-day-text":!0,"cki-day-con-text":n.continueSignInDay!=null&&n.holidaySignInDay==null})},[n.previewDay?(o(),r(D,{key:0},[z(v(n.month)+"."+v(n.day),1)],64)):(o(),r(D,{key:1},[n.continueSignInDay==null&&n.holidaySignInDay==null?(o(),r(D,{key:0},[z(v(n.month)+"."+v(n.day),1)],64)):c("v-if",!0),n.continueSignInDay!=null&&n.holidaySignInDay==null?(o(),r(D,{key:1},[z("\u8FDE\u7B7E"+v(n.continueSignInDay.day)+"\u5929",1)],64)):c("v-if",!0),n.holidaySignInDay!=null?(o(),r(D,{key:2},[z(v(n.holidaySignInDay.name),1)],64)):c("v-if",!0)],64))],2)),n.previewDay&&n.holidaySignInDay!=null?(o(),r("div",_a,v(n.holidaySignInDay.integralDesc),1)):c("v-if",!0)],4)):c("v-if",!0)],10,ra))),128)),X(i("div",{class:"cki-week-cover",onClick:re},ya,512),[[K,!s($)]])],6)]),i("div",wa,[i("div",{class:"cki-checkbtn",onClick:gt},[s(de)!==1?(o(),r("img",{key:0,src:s(k).checkinBtnImg,class:"cki-check-img"},null,8,Da)):c("v-if",!0),s(de)===1?(o(),r("span",{key:1,class:M({"cki-check-button":!0,"cki-button-disabled":s(de)===1})},v(s(de)===1?"\u5DF2\u7B7E\u5230\uFF0C\u660E\u65E5\u518D\u6765":"\u7ACB\u5373\u7B7E\u5230"),3)):c("v-if",!0)]),X(i("div",{class:"cki-toggle-group",onClick:re},[i("span",null,v(s($)?"\u6536\u8D77":"\u5C55\u5F00"),1),i("span",{class:"iconfont cki-toggle-icon",style:B({transform:s($)?"rotate(0deg)":"rotate(180deg)"})},"\uE7C0",4)],512),[[K,s($)]])])]),!s(S)&&!s(b)?(o(),r("div",{key:0,class:"checkin-cover",style:B({height:s($)?`${s(ve)*19.96+51.1}vw`:`${51.1}vw`})},[x(l,{path:`pages/webview/index.html?url=${encodeURIComponent(s(nt))}`,style:{position:"absolute",top:"0",left:"0",width:"100%",height:"100%",display:"block",color:"transparent"},username:s(ne)},{default:C(()=>[(o(),F(q("script"),{type:"text/wxtag-template"},{default:C(()=>[Ca,(o(),F(q("style"),null,{default:C(()=>[z(" .test-inner{color:transparent;width:100vw;height:100vh;position:relative;font-size:20px;} ")]),_:1}))]),_:1}))]),_:1},8,["path","username"])],4)):c("v-if",!0),x(Vt,{class:"ad-part",adPositionCodes:s(Pe),pageType:"signinpage"},null,8,["adPositionCodes"]),s(t).actInfo.showTaskList===1?(o(),F(Nt,{key:1,ref_key:"misscenter",ref:Be,onSensorTrack:lt,class:"mission-part",channelType:"user_task_center",taskFoldCount:s(t).actInfo.taskFoldCount,configData:s(k)},null,8,["taskFoldCount","configData"])):c("v-if",!0)]),x(s(Pt),{show:s(t).rulesVisible,"onUpdate:show":a[6]||(a[6]=n=>s(t).rulesVisible=n),title:"\u6D3B\u52A8\u89C4\u5219"},{default:C(()=>[i("div",{class:"rules-protocol",innerHTML:s(t).actInfo.instructions},null,8,Ia),i("div",ba,[i("div",{class:"rules-btn",onClick:a[5]||(a[5]=N(n=>{s(t).rulesVisible=!1},["stop"]))},"\u6211\u77E5\u9053\u4E86")])]),_:1},8,["show"]),x(ts,{show:s(Dt),onCloseDialog:Je},{default:C(()=>[i("div",Sa,[i("img",{src:s(k).checkinPopupBg,alt:"",class:"ck-dialog-bg"},null,8,Fa),Ea,i("div",$a,v(s(It)),1),s(Ge)?(o(),r("div",xa,v(s(Ge)),1)):c("v-if",!0),s(T).length===1?(o(),r("div",Ta,[i("img",{class:"ck-coin-single",src:s(k).popupPointsIcon,alt:""},null,8,Ba),i("div",Ma,"\u83B7\u5F97"+v(s(T)[0]&&s(T)[0].prizeValue)+"\u79EF\u5206",1)])):c("v-if",!0),s(T).length>1?(o(),r("div",{key:2,class:M({"ck-dialog-reward-multi":!0,"ck-dialog-reward-column":s(T).length>2||s(T).filter(n=>n.prizeType!==3).length===0})},[s(T).filter(n=>n.prizeType!==3).length===0?(o(),r(D,{key:0},[i("img",{class:"ck-coin-multi",src:s(k).popupPointsIcon,alt:""},null,8,Aa),i("div",La,v(s(Ee)),1),i("div",Pa,"("+v(s($e))+")",1)],64)):(o(),r(D,{key:1},[s(T).length>2?(o(!0),r(D,{key:0},H(s(T),(n,h)=>(o(),r("div",{class:"ck-dialog-rewarditem",key:h},[i("img",{src:n.prizeType===3?s(k).popupPointsIcon:n.prizeImg,alt:""},null,8,za),i("span",null,v(n.rewardText),1)]))),128)):(o(!0),r(D,{key:1},H(s(T),(n,h)=>(o(),r("div",{class:"ck-dialog-double",key:h},[i("img",{src:n.prizeType===3?s(k).popupPointsIcon:n.prizeImg,alt:""},null,8,Na),i("div",null,v(n.prizeType===3?"\u83B7\u5F97":"")+v(n.rewardText),1)]))),128))],64))],2)):c("v-if",!0),i("div",{class:M({"ck-dialog-btn":!0}),onClick:_t},Ra),c(" \u5173\u95ED\u6309\u94AE "),c(' <img src="https://m-cdn-lv.topsports.com.cn/fe/mp/member_images/icon-reward-close.png" class="reward-close" @click.stop="state.checkinDialogVisible = false" v-if="!reminderStatus || rewardList.length>=2"> ')])]),_:1},8,["show"]),x(os,{show:s(Ct),confinData:s(k),onCloseDialog:a[8]||(a[8]=n=>s(t).rewardDialogVisible=!1)},{default:C(()=>[i("div",Ua,[i("img",{src:s(he),alt:"",class:"ck-dialog-bg"},null,8,Ya),i("img",{src:"https://m-cdn-lv.topsports.com.cn/fe/mp/member_images/icon-reward-close.png",class:"reward-close",onClick:a[7]||(a[7]=N(n=>s(t).rewardDialogVisible=!1,["stop"]))})])]),_:1},8,["show","confinData"])])):c("v-if",!0),s(A)&&!s(R)?(o(),F(vs,{key:1})):c("v-if",!0),s(A)?c("v-if",!0):(o(),F(Ss,{key:2}))],4)}}});var oi=Z(ja,[["__scopeId","data-v-2e655920"],["__file","/data/jenkins/workspace/workspace/elease_shopYellowPage_20250730_7/src/pages/Interactive/dailyCenter/index.vue"]]);export{oi as default};
