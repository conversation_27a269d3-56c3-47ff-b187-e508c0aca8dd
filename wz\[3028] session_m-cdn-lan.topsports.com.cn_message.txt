GET /m/assets/index.c3fdf902.css h2
host: m-cdn-lan.topsports.com.cn
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
accept: text/css,*/*;q=0.1
sec-fetch-site: same-site
sec-fetch-mode: no-cors
sec-fetch-dest: style
referer: https://m.topsports.com.cn/
accept-encoding: gzip, deflate, br
accept-language: zh-CN,zh;q=0.9
cookie: appletsSource=wx71a6af1f91734f18
cookie: memberId=8a7a099f80db7ca40180eed6f3203bc2
cookie: version=4.5.2
cookie: sensorsdata2015jssdkcross=%7B%22%24device_id%22%3A%22198608b0b2c4fb-0489604a2a8fb28-673e1615-1440000-198608b0b2d67d%22%7D
cookie: QZ_SID=8191659A9B8535AAEBF0407A2D620919
cookie: Authorization=12d685a2-61e1-402b-91ac-97f32abaf6ae
priority: u=0



h2 200
server: Tengine
content-type: text/css
content-length: 1775
date: Thu, 31 Jul 2025 12:51:01 GMT
x-trace-id: 123-70109-1753966261211-6810203
last-modified: Thu, 31 Jul 2025 10:29:38 GMT
etag: "688b4592-6ef"
accept-ranges: bytes
via: cache69.l2cn3008[0,0,200-0,H], cache67.l2cn3008[1,0], cache67.l2cn3008[1,0], cache12.cn7330[62,79,200-0,M], cache2.cn7330[81,0]
age: 144
ali-swift-global-savetime: 1753966261
x-cache: MISS TCP_REFRESH_MISS dirn:9:368945155
x-swift-savetime: Thu, 31 Jul 2025 12:53:25 GMT
x-swift-cachetime: 862
access-control-allow-origin: *
timing-allow-origin: *
eagleid: 77243f1617539664055497389e

:root{--van-switch-size: 30px;--van-switch-width: 2em;--van-switch-height: 1em;--van-switch-node-size: 1em;--van-switch-node-background-color: var(--van-white);--van-switch-node-box-shadow: 0 3px 1px 0 rgba(0, 0, 0, .05), 0 2px 2px 0 rgba(0, 0, 0, .1), 0 3px 3px 0 rgba(0, 0, 0, .05);--van-switch-background-color: var(--van-background-color-light);--van-switch-on-background-color: var(--van-primary-color);--van-switch-transition-duration: var(--van-animation-duration-base);--van-switch-disabled-opacity: var(--van-disabled-opacity);--van-switch-border: var(--van-border-width-base) solid rgba(0, 0, 0, .1)}.van-switch{position:relative;display:inline-block;box-sizing:content-box;width:var(--van-switch-width);height:var(--van-switch-height);font-size:var(--van-switch-size);background:var(--van-switch-background-color);border:var(--van-switch-border);border-radius:var(--van-switch-node-size);cursor:pointer;transition:background-color var(--van-switch-transition-duration)}.van-switch__node{position:absolute;top:0;left:0;width:var(--van-switch-node-size);height:var(--van-switch-node-size);font-size:inherit;background:var(--van-switch-node-background-color);border-radius:100%;box-shadow:var(--van-switch-node-box-shadow);transition:transform var(--van-switch-transition-duration) cubic-bezier(.3,1.05,.4,1.05)}.van-switch__loading{top:25%;left:25%;width:50%;height:50%;line-height:1}.van-switch--on{background:var(--van-switch-on-background-color)}.van-switch--on .van-switch__node{transform:translate(calc(var(--van-switch-width) - var(--van-switch-node-size)))}.van-switch--on .van-switch__loading{color:var(--van-switch-on-background-color)}.van-switch--disabled{cursor:not-allowed;opacity:var(--van-switch-disabled-opacity)}.van-switch--loading{cursor:default}
